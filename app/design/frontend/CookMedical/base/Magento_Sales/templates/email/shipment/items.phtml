<?php
/** @var \Magento\Sales\Block\Order\Email\Items $block */
?>

<?php $_shipment = $block->getShipment() ?>
<?php $_order    = $block->getOrder() ?>
<?php if ($_shipment && $_order) : ?>
    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td style="padding:0 0 20px;">
                <table style="width:100%; border-collapse:collapse; font-size:14px;">
                    <thead>
                    <tr>
                        <th style="text-align:left; border-bottom:1px solid #e6e6e6; padding:8px;">
                            <?= $block->escapeHtml(__('Items')) ?>
                        </th>
                        <th style="text-align:center; border-bottom:1px solid #e6e6e6; padding:8px;">
                            <?= $block->escapeHtml(__('Qty')) ?>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($_shipment->getAllItems() as $index =>$_item) : ?>
                            <tr style="border-bottom: <?= ($index === array_key_last($_shipment->getAllItems())) ? 'none' : '1px solid #e6e6e6'; ?>;">
                                <td style="font-size:14px; padding:8px; text-align:left">
                                    <strong><?= $block->escapeHtml($_item->getName()) ?></strong><br>
                                    <?= $block->escapeHtml(__('SKU')) ?>: <?= $block->escapeHtml($_item->getSku()) ?>
                                </td>
                                <td style="font-size:14px; padding:8px; text-align:center">
                                    <?= (int)$_item->getOrderItem()->getQtyShipped() ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </td>
        </tr>
    </table>
<?php endif; ?>
