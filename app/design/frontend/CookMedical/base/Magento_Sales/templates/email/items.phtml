<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// phpcs:disable Magento2.Templates.ThisInTemplate

/** @var $block \Magento\Sales\Block\Order\Email\Items */

?>
<?php $_order = $block->getOrder(); ?>
<?php if ($_order) : ?>
    <?php $_items = $_order->getAllItems(); ?>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td style="padding:0 0 20px;">
                    <table style="width:100%; border-collapse:collapse; font-size:14px;">
                        <thead>
                            <tr>

                                <th style="text-align:left; border-bottom:1px solid #e6e6e6; padding:8px; width: 75%">
                                    <?= $block->escapeHtml(__('Items')) ?>
                                </th>
                                <th style="text-align:center; border-bottom:1px solid #e6e6e6; padding:8px;">
                                    <?= $block->escapeHtml(__('Qty')) ?>
                                </th>
                                <th style="text-align:right; border-bottom:1px solid #e6e6e6; padding:8px;">
                                    <?= $block->escapeHtml(__('Price')) ?>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($_items as $_item) : ?>
                        <tr>
                            <td style="font-size:14px; border-bottom:1px solid #e6e6e6; padding:8px; text-align:left; width: 75%">
                                <strong><?php echo $_item->getName(); ?></strong><br>
                                SKU: <?php echo $_item->getSku(); ?>
                            </td>
                            <td style="font-size:14px; border-bottom:1px solid #e6e6e6; padding:8px; text-align:center">
                                <?php echo $_item->getQtyOrdered(); ?>
                            </td>
                            <td style="font-size:14px; border-bottom:1px solid #e6e6e6; padding:8px; text-align:right">
                                $<?php echo $_item->getPrice(); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                            <!--Order totals -->
                            <tr>
                                <td colspan="3" style="background:#f7f7f7; padding:8px 0">
                                    <table style="width:100%; border-collapse:collapse; font-size:14px;">
                                        <tr>

                                            <td style="font-size:14px; padding:2px 8px; text-align:right; width: 82%">
                                                <?= $block->escapeHtml(__('Subtotal')) ?>
                                            </td>
                                            <td style="font-size:14px; padding:2px 8px; text-align:right">
                                                <?= $_order->formatPrice($_order->getSubtotal()) ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="font-size:14px; padding:2px 8px; text-align:right; width: 82%">
                                                <?= $block->escapeHtml(__('Shipping & Handling')) ?>
                                            </td>
                                            <td style="font-size:14px; padding:2px 8px; text-align:right">
                                                <?= $_order->formatPrice($_order->getShippingAmount()) ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="font-size:14px; padding:2px 8px; text-align:right; width: 82%">
                                                <strong>
                                                    <?= $block->escapeHtml(__('Grand Total')) ?>
                                                </strong>
                                            </td>
                                            <td style="font-size:14px; padding:2px 8px; text-align:right">
                                                <strong>
                                                    <?= $_order->formatPrice($_order->getGrandTotal()) ?>
                                                </strong>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <!--Order totals -->
                        </tbody>
                    </table>
                </td>
            </tr>
        </table>
<?php endif; ?>
