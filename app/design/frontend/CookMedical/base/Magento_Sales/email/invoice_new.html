<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Invoice for your %store_name order" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var order_data.customer_name":"Customer Name",
"var comment|escape|nl2br":"Invoice Comment",
"var invoice.increment_id":"Invoice Id",
"layout area=\"frontend\" handle=\"sales_email_order_invoice_items\" invoice=$invoice order=$order":"Invoice Items Grid",
"var order.increment_id":"Order Id",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var store.frontend_name":"Store Frontend Name",
"var store_phone":"Store Phone",
"var store_email":"Store Email",
"var store_hours":"Store Hours",
"var invoice": "Invoice",
"var order": "Order",
"var order_id": "Order DB Id",
"var invoice_id": "Invoice DB Id",
"var order_data.is_not_virtual": "Order Type"
} @-->

{{template config_path="design/email/header_template"}}

<tr>
    <td>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td class="content-container">
                    <p class="greeting">{{trans "%name," name=$order_data.customer_name}}</p>
                    <p>
                        {{trans "Thank you for your order from %store_name." store_name=$store.frontend_name}}
                        {{trans 'You can check the status of your order by <a href="%account_url">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
                    </p>
                    <p>
                        {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>.' store_email=$store_email |raw}}
                        {{depend store_hours}}
                        {{trans 'Our hours are <span class="no-link">%store_hours</span>.' store_hours=$store_hours |raw}}
                        {{/depend}}
                    </p>

                    <hr style="border:0; height:1px; background:#e6e6e6; margin:1rem 0;">
                    <p>
                        <strong style="font-size:18px">
                            {{trans "Your Invoice #%invoice_id for Order #%order_increment_id"
                            invoice_id=$invoice.increment_id order_increment_id=$order.increment_id}}
                        </strong><br>
                    </p>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="padding:0 0 20px;">
                                <table style="width:100%; border-collapse:collapse; font-size:14px;">
                                    <tr>
                                        <td width="50%" style="font-size:14px; padding-right:10px;">
                                            <strong>{{trans "Billing Info"}}</strong>
                                            <p>{{var formattedBillingAddress|raw}}</p>
                                        </td>
                                        <td width="50%" style="font-size:14px; padding-left:10px;">
                                            <strong>{{trans "Shipping Info"}}</strong>
                                            <p>{{var formattedShippingAddress|raw}}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="50%" style="font-size:14px; padding-right:10px;">
                                            <strong>{{trans "Payment Method"}}</strong>
                                            <p>{{var payment_html|raw}}</p>
                                        </td>
                                        <td width="50%" style="font-size:14px; padding-left:10px;">
                                            <strong>{{trans "Shipping Method"}}</strong>
                                            <p>{{var order.shipping_description}}</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>

                    {{layout handle="sales_email_order_items" invoice_id=$invoice_id order_id=$order_id area="frontend"}}
                </td>
            </tr>
        </table>
    </td>
</tr>
</table>
</td>
</tr>
</table>
{{template config_path="design/email/footer_template"}}
