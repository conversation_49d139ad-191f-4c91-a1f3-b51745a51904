<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Order confirmation – Cook Medical"}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var order_data.email_customer_note|escape|nl2br":"Email Order Note",
"var order.increment_id":"Order Id",
"layout handle=\"sales_email_order_items\" order=$order area=\"frontend\"":"Order Items Grid",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var shipping_msg":"Shipping message",
"var created_at_formatted":"Order Created At (datetime)",
"var store.frontend_name":"Store Frontend Name",
"var store_phone":"Store Phone",
"var store_email":"Store Email",
"var store_hours":"Store Hours",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var order_data.is_not_virtual":"Order Type",
"var order":"Order",
"var order_id": "Order DB Id",
"var order_data.customer_name":"Customer Name"
} @-->

{{template config_path="design/email/header_template"}}

<tr>
    <td>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td class="content-container">
                    <p>
                        {{var order_data.customer_name}},<br><br>
                        {{trans "Thank you for your order with %store_name Online Ordering. Your order information is shown below." store_name=$store.frontend_name}}
                        {{trans "If you have questions about your order, you can email us at"}}
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                        {{depend store_phone}} {{trans 'or call us at <strong>%store_phone</strong>' store_phone=$store_phone |raw}}{{/depend}}.
                        {{depend store_hours}}
                        {{trans 'Our hours are <span class="no-link">%store_hours</span>.' store_hours=$store_hours |raw}}
                        {{/depend}}
                    </p>
                    <p>
                        {{trans "Once your package ships, we will send you a tracking number. You can check the status of your order by"}}
                        <a href="{{var this.getUrl($store,'customer/account/',[_nosid:1])}}">{{trans "logging into your account"}}</a>
                    </p>
                    <hr style="border:0; height:1px; background:#e6e6e6; margin:1rem 0;">
                    <p>
                        <strong style="font-size:18px">{{trans "Your Order #%order_id" order_id=$order.increment_id}}</strong><br>
                        {{trans "Placed on %created_at" created_at=$created_at_formatted}}
                    </p>

                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="padding:0 0 20px;">
                                <table style="width:100%; border-collapse:collapse; font-size:14px;">
                                    <tr>
                                        <td width="50%" style="font-size:14px; padding-right:10px;">
                                            <strong>{{trans "Billing Info"}}</strong>
                                            <p>{{var formattedBillingAddress|raw}}</p>
                                        </td>
                                        <td width="50%" style="font-size:14px; padding-left:10px;">
                                            <strong>{{trans "Shipping Info"}}</strong>
                                            <p>{{var formattedShippingAddress|raw}}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="payment" width="50%" style="font-size:14px; padding-right:10px;">
                                            <strong>{{trans "Payment Method"}}</strong>
                                            <p>{{var payment_html|raw}}</p>
                                        </td>
                                        <td width="50%" style="font-size:14px; padding-left:10px;">
                                            <strong>{{trans "Shipping Method"}}</strong>
                                            <p>{{var order.shipping_description}}</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>

                    {{layout handle="sales_email_order_items" order_id=$order_id area="frontend"}}
                </td>
            </tr>
        </table>
    </td>
</tr>
</table>
</td>
</tr>
</table>
{{template config_path="design/email/footer_template"}}
