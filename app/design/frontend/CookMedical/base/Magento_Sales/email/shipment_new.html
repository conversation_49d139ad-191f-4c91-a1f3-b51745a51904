<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Your order has shipped – Cook Medical"}} @-->
<!--@vars {
    "var formattedBillingAddress|raw":"Billing Address",
    "var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
    "var order_data.customer_name":"Customer Name",
    "var order.increment_id":"Order Id",
    "var payment_html|raw":"Payment Details",
    "var comment|escape|nl2br":"Shipment Comment",
    "var shipment.increment_id":"Shipment Id",
    "layout handle=\"sales_email_order_shipment_items\" shipment=$shipment order=$order":"Shipment Items Grid",
    "block class='Magento\\\\Framework\\\\View\\\\Element\\\\Template' area='frontend' template='Magento_Sales::email/shipment/track.phtml' shipment=$shipment order=$order":"Shipment Track Details",
    "var formattedShippingAddress|raw":"Shipping Address",
    "var order.shipping_description":"Shipping Description",
    "var store.frontend_name":"Store Frontend Name",
    "var store_phone":"Store Phone",
    "var store_email":"Store Email",
    "var store_hours":"Store Hours",
    "var order_data.is_not_virtual": "Order Type",
    "var shipment": "Shipment",
    "var order": "Order",
    "var order_id": "Order DB Id",
    "var shipment_id": "Shipment DB Id"
} @-->

{{template config_path="design/email/header_template"}}

<tr>
    <td>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td class="content-container">
                    <p>
                        {{var order_data.customer_name}},<br><br>
                        {{trans "Your recent order with %store_name Online Ordering has shipped and the details are shown below." store_name=$store.frontend_name}}
                        {{trans "You can check the status of your order by "}}
                        <a href="{{var this.getUrl($store,'customer/account/',[_nosid:1])}}">{{trans "logging into your account"}}</a>
                    </p>
                    <p>
                        {{trans "If you have questions about your order, you can email us at "}}
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                        {{depend store_phone}} {{trans 'or call us at <strong>%store_phone</strong>' store_phone=$store_phone |raw}}{{/depend}}.
                        {{depend store_hours}}
                        {{trans 'Our hours are <span class="no-link">%store_hours</span>.' store_hours=$store_hours |raw}}
                        {{/depend}}
                    </p>
                    <p>
                        {{trans "Thank you for ordering from Cook."}}
                    </p>
                    <hr style="border:0; height:1px; background:#e6e6e6; margin:1rem 0;">

                    <!-- Shipment & Order Info -->
                    <p style="margin: 0">
                        <strong style="font-size:18px">
                            {{trans "Your Shipment #%shipment_id for Order #%order_increment_id"
                            shipment_id=$shipment.increment_id order_increment_id=$order.increment_id}}
                        </strong><br>
                    </p>

                    <!-- Billing & Shipping, Payment & Shipping Method -->
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">

                        {{depend comment}}
                        <tr>
                            <td>
                                <table class="message-info">
                                    <tr>
                                        <td>
                                            {{var comment|escape|nl2br}}
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        {{/depend}}
                        {{layout handle="sales_email_order_shipment_track" shipment_id=$shipment_id order_id=$order_id}}

                        <tr>
                            <td style="padding:20px 0;">
                                <table style="width:100%; border-collapse:collapse; font-size:14px;">
                                    <tr>
                                        <td width="50%" style="font-size:14px; padding-right:10px;">
                                            <strong>{{trans "Billing Info"}}</strong>
                                            <p>{{var formattedBillingAddress|raw}}</p>
                                        </td>
                                        <td width="50%" style="font-size:14px; padding-left:10px;">
                                            <strong>{{trans "Shipping Info"}}</strong>
                                            <p>{{var formattedShippingAddress|raw}}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="payment" width="50%" style="font-size:14px; padding-right:10px;">
                                            <strong>{{trans "Payment Method"}}</strong>
                                            <p>{{var payment_html|raw}}</p>
                                        </td>
                                        <td width="50%" style="font-size:14px; padding-left:10px;">
                                            <strong>{{trans "Shipping Method"}}</strong>
                                            <p>{{var order.shipping_description}}</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    {{layout handle="sales_email_order_shipment_items" shipment_id=$shipment_id order_id=$order_id}}

                </td>
            </tr>
        </table>
    </td>
</tr>
</table>
</td>
</tr>
</table>

{{template config_path="design/email/footer_template"}}
