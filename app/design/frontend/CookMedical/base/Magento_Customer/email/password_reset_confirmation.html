<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Reset your Cook Medical Online Ordering password"}} @-->
<!--@vars {
"var store.frontend_name":"Store Name",
"var customer.name":"Customer Name",
"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])":"Reset Password URL"
} @-->
{{template config_path="design/email/header_template"}}

<tr>
    <td>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td class="content-container">
                    <p>
                        {{trans "Hello %name" name=$customer.name}},
                        <br>
                        <br>
                        {{trans 'A password reset was requested for your user account <a style="text-decoration: underline" href="mailto:%email">%email</a> on the Cook Medical Online Ordering system.' email=$customer.email|raw}}
                    <p>
                    <p>
                        <a style="text-decoration: underline" href="{{var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}" target="_blank">{{trans "Reset your password"}}</a>
                    </p>
                    <p>
                        {{trans "The link will expire in 5 days."}}
                    </p>
                    <p>{{trans "If you do not want to reset your password, please disregard this email."}}</p>
                    <p>{{trans "If you did not request this password reset, please contact our Customer Support team at the phone or email below."}}</p>
                    <p>
                        {{trans "Thank you,"}}
                        <br>
                        {{trans "Cook Medical"}}
                    </p>
                </td>
            </tr>
        </table>
    </td>
</tr>
</table>
</td>
</tr>
</table>


{{template config_path="design/email/footer_template"}}
