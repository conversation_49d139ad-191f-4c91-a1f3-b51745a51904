<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Please set your password to complete sign up"}} @-->
<!--@vars {
"var store.frontend_name":"Store Name",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])":"Password Reset URL",
"var customer.email":"Customer Email",
"var customer.name":"Customer Name"
} @-->

{{template config_path="design/email/header_template"}}

<tr>
    <td>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td class="content-container">
                    <p>
                        {{trans "%name,
                        <br>
                        <br>
                        Welcome to the Cook Medical Online Ordering system! Your account has been set up, and you can now sign in and set a password:" name=$customer.name|raw}}
                    </p>
                    <ul>
                        <li>
                            {{trans
                            '<strong>Sign in</strong>: <a href="%create_password_url">LINK</a>'
                            create_password_url="$this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])"
                            |raw}}
                        </li>
                        <li>{{trans '<strong>Your account email:</strong>'|raw}} <strong>{{var customer.email}}</strong></li>
                    </ul>

                    <p>
                        {{trans "When you sign in to your account, you will be able to:"}}
                    </p>
                    <ul>
                        <li>{{trans "Place standard and expedited orders"}}</li>
                        <li>{{trans "Check the status of orders placed in the system"}}</li>
                        <li>{{trans "View past orders and reorder with a click"}}</li>
                        <li>{{trans "Save favorites for convenient reorder"}}</li>
                    </ul>
                    <p>
                        {{trans "Thank you,"}}
                        <br />
                        {{trans "Cook Medical"}}
                    </p>
                </td>
            </tr>
        </table>
    </td>
</tr>
</table>
</td>
</tr>
</table>


{{template config_path="design/email/footer_template"}}
