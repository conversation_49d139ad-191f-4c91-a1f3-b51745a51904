<?php
/**
 * @category Cook Medical
 * @package CookMedical/Magento_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \CookMedical\Checkout\ViewModel\LastVisitedPlp $lastVisitedPlp
 */
$lastVisitedPlp = $block->getData('last_visited_plp');
?>

<div class="primary">
    <a href="<?= $block->escapeUrl($lastVisitedPlp->getRedirectUrl()) ?>"
        class="action primary add-more-products"
        title="<?= $block->escapeHtmlAttr($lastVisitedPlp->getButtonLabel()) ?>"
        id="add-more-products-link">
        <span><?= $block->escapeHtml($lastVisitedPlp->getButtonLabel()) ?></span>
    </a>
</div>

<script>
require(['jquery', 'CookMedical_Checkout/js/last-visited-plp-tracker'], function ($, plpTracker) {
    'use strict';

    $(document).ready(function() {
        var $addMoreLink = $('#add-more-products-link');
        var storedUrl = plpTracker.getStoredPlpUrl();

        if (storedUrl && $addMoreLink.length) {
            $addMoreLink.attr('href', storedUrl);
        }
    });
});
</script>
