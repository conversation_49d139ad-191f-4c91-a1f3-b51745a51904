<?php
/**
 * @category Cook Medical
 * @package CookMedical/Magento_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \CookMedical\Checkout\ViewModel\LastVisitedPlp $lastVisitedPlp
 */
$lastVisitedPlp = $block->getData('last_visited_plp');
?>

<!-- ALWAYS SHOW THE BUTTON -->
<div class="actions-toolbar add-more-products-wrapper">
    <div class="primary">
        <a href="<?= $block->escapeUrl($lastVisitedPlp ? $lastVisitedPlp->getRedirectUrl() : $block->getUrl('shop-all')) ?>"
           class="action primary add-more-products"
           title="<?= $block->escapeHtmlAttr($lastVisitedPlp ? $lastVisitedPlp->getButtonLabel() : 'Add More Products') ?>"
           id="add-more-products-link">
            <span><?= $block->escapeHtml($lastVisitedPlp ? $lastVisitedPlp->getButtonLabel() : 'Add More Products') ?></span>
        </a>
    </div>
</div>

<script>
// Handle PLP URL persistence with localStorage fallback - following existing project pattern
require(['jquery'], function ($) {
    'use strict';

    $(document).ready(function() {
        var storageKey = 'cookmedical_last_plp_url';
        var currentUrl = '<?= $lastVisitedPlp ? $lastVisitedPlp->getLastVisitedPlpUrl() : '' ?>';
        var $addMoreLink = $('#add-more-products-link');

        // Save current URL to localStorage if we have one from session
        if (currentUrl) {
            try {
                localStorage.setItem(storageKey, currentUrl);
                console.log('CookMedical: Saved PLP URL to localStorage:', currentUrl);
            } catch (e) {
                console.error('CookMedical: Error saving to localStorage:', e);
            }
        } else {
            // If no session URL, try to use localStorage as fallback
            try {
                var storedUrl = localStorage.getItem(storageKey);
                if (storedUrl && $addMoreLink.length) {
                    console.log('CookMedical: Using localStorage URL as fallback:', storedUrl);
                    $addMoreLink.attr('href', storedUrl);
                }
            } catch (e) {
                console.error('CookMedical: Error reading from localStorage:', e);
            }
        }
    });
});
</script>
