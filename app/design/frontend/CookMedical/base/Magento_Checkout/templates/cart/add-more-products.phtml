<?php
/**
 * @category Cook Medical
 * @package CookMedical/Magento_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2025 Scandiweb, Ltd (http://scandiweb.com)
 */

/**
 * @var \CookMedical\Checkout\ViewModel\LastVisitedPlp $lastVisitedPlp
 */
$lastVisitedPlp = $block->getData('last_visited_plp');
?>

<div class="primary">
    <a href="<?= $block->escapeUrl($lastVisitedPlp->getRedirectUrl()) ?>"
        class="action primary add-more-products"
        title="<?= $block->escapeHtmlAttr($lastVisitedPlp->getButtonLabel()) ?>"
        id="add-more-products-link">
        <span><?= $block->escapeHtml($lastVisitedPlp->getButtonLabel()) ?></span>
    </a>
</div>

<script>
require(['jquery', 'CookMedical_Checkout/js/last-visited-plp-tracker'], function ($, plpTracker) {
    'use strict';

    $(document).ready(function() {
        var storedUrl = plpTracker.getStoredPlpUrl();
        if (storedUrl) {
            $('#add-more-products-link').attr('href', storedUrl);
        }
    });
});
</script>
