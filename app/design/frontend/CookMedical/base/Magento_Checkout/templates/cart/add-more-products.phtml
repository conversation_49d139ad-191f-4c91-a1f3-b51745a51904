<?php
/**
 * @category Cook Medical
 * @package CookMedical/Magento_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \CookMedical\Checkout\ViewModel\LastVisitedPlp $lastVisitedPlp
 */
$lastVisitedPlp = $block->getData('last_visited_plp');
?>

<?php if ($lastVisitedPlp && $lastVisitedPlp->hasLastVisitedPlp()): ?>
<div class="actions-toolbar add-more-products-wrapper">
    <div class="primary">
        <a href="<?= $block->escapeUrl($lastVisitedPlp->getRedirectUrl()) ?>"
           class="action primary add-more-products"
           title="<?= $block->escapeHtmlAttr($lastVisitedPlp->getButtonLabel()) ?>">
            <span><?= $block->escapeHtml($lastVisitedPlp->getButtonLabel()) ?></span>
        </a>
    </div>
</div>
<?php endif; ?>

<script>
// Simple local storage backup for PLP URL - following existing project pattern
require(['jquery'], function ($) {
    'use strict';

    $(document).ready(function() {
        var storageKey = 'cookmedical_last_plp_url';
        var currentUrl = '<?= $lastVisitedPlp ? $lastVisitedPlp->getLastVisitedPlpUrl() : '' ?>';

        // Save current URL to localStorage if we have one
        if (currentUrl) {
            try {
                localStorage.setItem(storageKey, currentUrl);
                console.log('CookMedical: Saved PLP URL to localStorage:', currentUrl);
            } catch (e) {
                console.error('CookMedical: Error saving to localStorage:', e);
            }
        }

        // If no current URL but we have one in localStorage, show button anyway
        if (!currentUrl) {
            try {
                var storedUrl = localStorage.getItem(storageKey);
                if (storedUrl) {
                    console.log('CookMedical: Found PLP URL in localStorage:', storedUrl);
                    // Show the button with the stored URL
                    if ($('.add-more-products-wrapper').length === 0) {
                        var buttonHtml = '<div class="actions-toolbar add-more-products-wrapper">' +
                            '<div class="primary">' +
                            '<a href="' + storedUrl + '" class="action primary add-more-products" title="Add More Products">' +
                            '<span>Add More Products</span>' +
                            '</a>' +
                            '</div>' +
                            '</div>';
                        $('.cart.table-wrapper').after(buttonHtml);
                    }
                }
            } catch (e) {
                console.error('CookMedical: Error reading from localStorage:', e);
            }
        }
    });
});
</script>
