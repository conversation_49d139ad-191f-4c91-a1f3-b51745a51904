<?php
/**
 * @category Cook Medical
 * @package CookMedical/Magento_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \CookMedical\Checkout\ViewModel\LastVisitedPlp $lastVisitedPlp
 */
$lastVisitedPlp = $block->getData('last_visited_plp');
?>

<?php if ($lastVisitedPlp): ?>
<!-- ALWAYS SHOW THE BUTTON -->
<div class="actions-toolbar add-more-products-wrapper">
    <div class="primary">
        <a href="<?= $block->escapeUrl($lastVisitedPlp->getRedirectUrl()) ?>"
           class="action primary add-more-products"
           title="<?= $block->escapeHtmlAttr($lastVisitedPlp->getButtonLabel()) ?>">
            <span><?= $block->escapeHtml($lastVisitedPlp->getButtonLabel()) ?></span>
        </a>
    </div>
</div>

<!-- SIMPLIFIED DEBUG INFO -->
<div style="background: #f0f0f0; padding: 15px; margin: 10px 0; font-size: 12px; border: 1px solid #ccc;">
    <strong>Debug Info (Generic Session):</strong><br>
    Has last visited PLP: <?= $lastVisitedPlp->hasLastVisitedPlp() ? 'YES' : 'NO' ?><br>
    Last URL: <?= $lastVisitedPlp->getLastVisitedPlpUrl() ?: 'NULL' ?><br>
    Redirect URL: <?= $block->escapeHtml($lastVisitedPlp->getRedirectUrl()) ?><br>

    <?php $debugInfo = $lastVisitedPlp->getDebugInfo(); ?>
    <br><strong>Saved URL:</strong><br>
    <?= $block->escapeHtml($debugInfo['saved_url'] ?: 'NULL') ?><br>

    <br><strong>Session Debug Info:</strong><br>
    <?= $block->escapeHtml(json_encode($debugInfo['debug_info'], JSON_PRETTY_PRINT)) ?><br>

    <br><strong>Current Request:</strong><br>
    <?= $block->escapeHtml(json_encode($debugInfo['current_request'], JSON_PRETTY_PRINT)) ?><br>

    <br><strong>Registry Info:</strong><br>
    <?= $block->escapeHtml(json_encode($debugInfo['registry_info'], JSON_PRETTY_PRINT)) ?><br>

    <br><strong>Is PLP Page:</strong> <?= $debugInfo['is_plp_page'] ? 'YES' : 'NO' ?><br>

    <br><strong>Session ID:</strong> <?= $block->escapeHtml($debugInfo['session_id']) ?>
</div>
<?php endif; ?>
