<?php
/**
 * @category Cook Medical
 * @package CookMedical/Magento_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \CookMedical\Checkout\ViewModel\LastVisitedPlp $lastVisitedPlp
 */
$lastVisitedPlp = $block->getData('last_visited_plp');
?>

<?php if ($lastVisitedPlp && $lastVisitedPlp->hasLastVisitedPlp()): ?>
<div class="actions-toolbar add-more-products-wrapper">
    <div class="primary">
        <a href="<?= $block->escapeUrl($lastVisitedPlp->getRedirectUrl()) ?>"
           class="action primary add-more-products"
           title="<?= $block->escapeHtmlAttr($lastVisitedPlp->getButtonLabel()) ?>">
            <span><?= $block->escapeHtml($lastVisitedPlp->getButtonLabel()) ?></span>
        </a>
    </div>
</div>
<?php endif; ?>
