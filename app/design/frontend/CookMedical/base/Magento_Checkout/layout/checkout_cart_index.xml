<?xml version="1.0"?>
<!--
/**
 * @category Cook Medical
 * @package CookMedical/Magento_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <move element="page.messages" destination="columns.top" before="page.main.title"/>
        <referenceContainer name="header-wrapper" htmlClass="header content header-content-checkout">
            <referenceBlock name="cart.discount" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.default.actions.edit" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.simple.actions.edit" remove="true"/>
            <referenceBlock name="checkout.cart.actions" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.default.actions.move_to_wishlist" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.simple.actions.move_to_wishlist" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.bundle.actions.move_to_wishlist" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.downloadable.actions.move_to_wishlist" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.grouped.actions.move_to_wishlist" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.configurable.actions.move_to_wishlist" remove="true"/>
            <referenceBlock name="checkout.cart.item.renderers.virtual.actions.move_to_wishlist" remove="true"/>
        </referenceContainer>
        <referenceContainer name="columns.top">
            <block class="Magento\Theme\Block\Html\Breadcrumbs" name="breadcrumbs" before="-"/>
        </referenceContainer>
        <referenceBlock name="page.main.title" display="true">
            <action method="setPageTitle">
                <argument name="title" translate="true" xsi:type="string">Cart</argument>
            </action>
            <block class="Magento\Framework\View\Element\Template" name="cart.count" template="Magento_Checkout::cart/count.phtml">
                <arguments>
                    <argument name="cart_count_view_model" xsi:type="object">\CookMedical\Checkout\ViewModel\CartProductCount</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceContainer name="cart.summary">
            <block class="Magento\Framework\View\Element\Template" name="checkout.cart.summary.title" before="-"
                   template="Magento_Theme::text.phtml">
                <arguments>
                    <argument translate="true" name="text" xsi:type="string">Order Summary</argument>
                    <argument name="tag" xsi:type="string">strong</argument>
                    <argument name="css_class" xsi:type="string">summary title</argument>
                </arguments>
            </block>
        </referenceContainer>
        <referenceBlock name="breadcrumbs">
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Home</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Home</item>
                    <item name="label" xsi:type="string">Home</item>
                    <item name="link" xsi:type="string">{{baseUrl}}</item>
                </argument>
            </action>
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Shopping Cart</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Shopping Cart</item>
                    <item name="label" xsi:type="string">Shopping Cart</item>
                </argument>
            </action>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.simple">
            <arguments>
                <argument name="tooltip_text" xsi:type="object">\CookMedical\MagentoTheme\ViewModel\TooltipConfig
                </argument>
                <argument name="cart_product_count" xsi:type="object">\CookMedical\Checkout\ViewModel\CartProductCount</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.item.renderers.default">
            <arguments>
                <argument name="tooltip_text" xsi:type="object">\CookMedical\MagentoTheme\ViewModel\TooltipConfig
                </argument>
                <argument name="cart_product_count" xsi:type="object">\CookMedical\Checkout\ViewModel\CartProductCount</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="checkout.cart.form">
            <arguments>
                <argument name="cart_count_view_model" xsi:type="object">CookMedical\Checkout\ViewModel\CartProductCount</argument>
            </arguments>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template" name="checkout.cart.add.more.products"
                   after="checkout.cart.form" template="Magento_Checkout::cart/add-more-products.phtml">
                <arguments>
                    <argument name="last_visited_plp" xsi:type="object">CookMedical\Checkout\ViewModel\LastVisitedPlp</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
