<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="header.container">
            <referenceBlock name="minicart" remove="true"/>
            <referenceBlock name="top.search" remove="true"/>
            <referenceContainer name="online-ordering" remove="true"/>
            <referenceContainer name="header-account-actions" remove="true"/>
            <referenceContainer name="header.panel.wrapper" remove="true"/>
        </referenceContainer>
        <referenceContainer name="header-wrapper">
            <block name="checkout-header-content" template="Magento_Checkout::checkout-header-content.phtml"/>
        </referenceContainer>
        <referenceContainer name="footer-container" remove="true"/>
        <referenceContainer name="page.wrapper">
            <referenceBlock name="copyright" remove="true"/>
        </referenceContainer>
        <referenceContainer name="footer">
            <referenceBlock name="footer_links" remove="true"/>
        </referenceContainer>
        <referenceBlock name="footer_links" remove="true"/>
        <referenceBlock name="footer_copyright_block" remove="true"/>
        <!-- Add shipping cost updater initialization -->
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template"
                   name="cookmedical.shipping.cost.updater.init"
                   template="CookMedical_Checkout::shipping-cost-updater-init.phtml" />
        </referenceContainer>

        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="sidebar" xsi:type="array">
                                    <item name="config" xsi:type="array">
                                        <item name="template" xsi:type="string">CookMedical_Checkout/sidebar</item>
                                        <item name="deps" xsi:type="array">
                                            <item name="0" xsi:type="string">checkout.steps</item>
                                        </item>
                                    </item>
                                    <item name="children" xsi:type="array">
                                        <item name="summary" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="totals" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="subtotal" xsi:type="array">
                                                            <item name="config" xsi:type="array">
                                                                <item name="title" xsi:type="string" translate="true">Subtotal</item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                                <item name="steps" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="placeorderbtn" xsi:type="array">
                                            <item name="component" xsi:type="string">CookMedical_Checkout/js/view/place-order</item>
                                            <item name="config" xsi:type="array">
                                                <item name="template" xsi:type="string">CookMedical_Checkout/place-order</item>
                                            </item>
                                            <item name="children" xsi:type="array">
                                                <item name="summary" xsi:type="array">
                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary</item>
                                                    <item name="displayArea" xsi:type="string">summary</item>
                                                    <item name="config" xsi:type="array">
                                                        <item name="template" xsi:type="string">Magento_Checkout/summary</item>
                                                    </item>
                                                    <item name="children" xsi:type="array">
                                                        <item name="totals" xsi:type="array">
                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/totals</item>
                                                            <item name="displayArea" xsi:type="string">totals</item>
                                                            <item name="config" xsi:type="array">
                                                                <item name="template" xsi:type="string">Magento_Checkout/summary/totals</item>
                                                            </item>
                                                            <item name="children" xsi:type="array">
                                                                <!-- sort order for this totals is configured on admin panel-->
                                                                <!-- Stores->Configuration->SALES->Sales->General->Checkout Totals Sort Order -->
                                                                <item name="subtotal" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/subtotal</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="title" xsi:type="string" translate="true">Subtotal</item>
                                                                    </item>
                                                                </item>
                                                                <item name="shipping" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/shipping</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="title" xsi:type="string" translate="true">Shipping</item>
                                                                        <item name="notCalculatedMessage" xsi:type="string" translate="true">Selected shipping method is not available. Please select another shipping method for this order.</item>
                                                                    </item>
                                                                </item>
                                                                <item name="grand-total" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/grand-total</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="title" xsi:type="string" translate="true">Order Total</item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                        <item name="itemsBefore" xsi:type="array">
                                                            <item name="component" xsi:type="string">uiComponent</item>
                                                            <item name="children" xsi:type="array">
                                                                <!-- merge your components here -->
                                                            </item>
                                                        </item>
                                                        <item name="cart_items" xsi:type="array">
                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/cart-items</item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="details" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="thumbnail" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details/thumbnail</item>
                                                                            <item name="displayArea" xsi:type="string">before_details</item>
                                                                        </item>
                                                                        <item name="subtotal" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details/subtotal</item>
                                                                            <item name="displayArea" xsi:type="string">after_details</item>
                                                                        </item>
                                                                        <item name="message" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details/message</item>
                                                                            <item name="displayArea" xsi:type="string">item_message</item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                        <item name="itemsAfter" xsi:type="array">
                                                            <item name="component" xsi:type="string">uiComponent</item>
                                                            <item name="children" xsi:type="array">
                                                                <!-- merge your components here -->
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                        <item name="shipping-step" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="shippingAddress" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="summary" xsi:type="array">
                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary</item>
                                                            <item name="displayArea" xsi:type="string">summary</item>
                                                            <item name="config" xsi:type="array">
                                                                <item name="template" xsi:type="string">Magento_Checkout/summary</item>
                                                            </item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="totals" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/totals</item>
                                                                    <item name="displayArea" xsi:type="string">totals</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="template" xsi:type="string">Magento_Checkout/summary/totals</item>
                                                                    </item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="subtotal" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/subtotal</item>
                                                                            <item name="config" xsi:type="array">
                                                                                <item name="title" xsi:type="string" translate="true">Subtotal</item>
                                                                            </item>
                                                                        </item>
                                                                        <item name="shipping" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/shipping</item>
                                                                            <item name="config" xsi:type="array">
                                                                                <item name="title" xsi:type="string" translate="true">Shipping</item>
                                                                                <item name="notCalculatedMessage" xsi:type="string" translate="true">Selected shipping method is not available. Please select another shipping method for this order.</item>
                                                                            </item>
                                                                        </item>
                                                                        <item name="grand-total" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/grand-total</item>
                                                                            <item name="config" xsi:type="array">
                                                                                <item name="title" xsi:type="string" translate="true">Order Total</item>
                                                                            </item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                                <item name="itemsBefore" xsi:type="array">
                                                                    <item name="component" xsi:type="string">uiComponent</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <!-- merge your components here -->
                                                                    </item>
                                                                </item>
                                                                <item name="cart_items" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/cart-items</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="details" xsi:type="array">
                                                                            <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details</item>
                                                                            <item name="children" xsi:type="array">
                                                                                <item name="thumbnail" xsi:type="array">
                                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details/thumbnail</item>
                                                                                    <item name="displayArea" xsi:type="string">before_details</item>
                                                                                </item>
                                                                                <item name="subtotal" xsi:type="array">
                                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details/subtotal</item>
                                                                                    <item name="displayArea" xsi:type="string">after_details</item>
                                                                                </item>
                                                                                <item name="message" xsi:type="array">
                                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/view/summary/item/details/message</item>
                                                                                    <item name="displayArea" xsi:type="string">item_message</item>
                                                                                </item>
                                                                            </item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                                <item name="itemsAfter" xsi:type="array">
                                                                    <item name="component" xsi:type="string">uiComponent</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <!-- merge your components here -->
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                        <item name="billing-step" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="payment" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="afterMethods" xsi:type="array">
                                                            <item name="children" xsi:type="array">
                                                                <item name="discount" xsi:type="array">
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="componentDisabled" xsi:type="boolean">true</item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                        <item name="beforeMethods" xsi:type="array">
                                                            <item name="children" xsi:type="array">
                                                                <item name="contact_and_shipping" xsi:type="array">
                                                                    <item name="component" xsi:type="string">CookMedical_Checkout/js/view/contact-and-shipping</item>
                                                                    <item name="displayArea" xsi:type="string">contactAndShipping</item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
