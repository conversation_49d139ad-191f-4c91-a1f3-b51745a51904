/**
 * @category Cook Medical
 * @package frontend/CookMedical/Magento_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

// Common
& when (@media-common = true) {
    .checkout-index-index {
        // Shipping cost update animations
        .totals.shipping {
            .price {
                transition: opacity 0.3s ease;
                
                &.updating {
                    opacity: 0.6;
                    position: relative;
                    
                    &::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        right: -20px;
                        transform: translateY(-50%);
                        width: 12px;
                        height: 12px;
                        border: 2px solid @color-cook-red;
                        border-top: 2px solid transparent;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                }
            }
        }
        
        // Loading animation
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
        
        // Shipping method selection feedback
        .shipping-method-list {
            .method-row {
                transition: background-color 0.2s ease;
                
                &.active-method {
                    background-color: fade(@color-cook-red, 5%);
                    border-left: 3px solid @color-cook-red;
                }
            }
        }
    }
}
