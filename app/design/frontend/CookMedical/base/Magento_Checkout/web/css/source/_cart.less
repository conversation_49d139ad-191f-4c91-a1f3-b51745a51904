/**
 * @category Cook Medical
 * @package frontend/CookMedical/Magento_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

// Common
& when (@media-common = true) {
    .checkout-cart-index {
        main.page-main {
            .page-title-wrapper {
                padding-top: 0;

                .page-title {
                    padding-top: 40px;

                    .base {
                        line-height: 38px;
                    }
                }

                .cart-count {
                    display: none;
                }
            }

            .qty-value {
                border-radius: unset;
            }

            .columns {
                position: relative;

                &:before {
                    content: '';
                    position: absolute;
                    transform: translateX(-50%);
                    left: 50%;
                    z-index: -1;
                    background-color: @color-cook-light-gray;
                    height: 100%;
                    width: 100vw;
                }
            }
        }

        .table-wrapper {
            margin-bottom: 0;
        }

        .page-title-wrapper {
            position: relative;

            &:before {
                content: '';
                position: absolute;
                transform: translateX(-50%);
                left: 50%;
                z-index: -1;
                background-color: @color-cook-light-gray;
                height: 100%;
                width: 100vw;
            }

        }

        .cart-summary {
            margin-bottom: 0;
            padding-bottom: 0;
            background: @color-cook-light-gray;

            .totals.shipping {
                display: none;
            }

            .cart-totals {
                border: none;
            }

            .title {
                font-family: @cook-font-avenir-next-demi;
            }

            .table-wrapper {
                margin: 0 -22px
            }

            .sub {
                .mark {
                    background: @color-white;
                    border-radius: 3px 0 0 3px;
                    padding: 18px 0 16px 16px;
                    font-size: 16px;
                    font-weight: 400;
                    line-height: 24px;
                }

                .amount {
                    background: @color-white;
                    padding: 16px 16px 16px 0;
                    border-radius: 0 3px 3px 0;

                    .price {
                        font-family: @cook-font-avenir-next-demi;
                        padding-top: 2px;
                        font-size: 16px;
                        line-height: 24px;
                    }
                }
            }

            .grand {
                .amount {
                    background: @color-white;
                    border-top-right-radius: 3px;
                    padding: 16px 16px 12px 0;
                    border-top: 2px solid @color-cook-light-gray;
                    line-height: 24px;

                    strong {
                        padding: 2px 0 0 0;
                        font-size: 20px;
                        font-family: @cook-font-avenir-next-demi;
                    }
                }

                .mark {
                    background: @color-white;
                    border-top-left-radius: 3px;
                    border-top: 2px solid @color-cook-light-gray;
                    line-height: 24px;
                    padding: 16px 0 12px 16px;

                    strong {
                        padding: 2px 0 0 0;
                        font-family: @cook-font-avenir-next-regular;
                        font-weight: 400;
                        font-size: 16px;
                    }
                }
            }

            .checkout.checkout-methods-items {
                background: @color-white;
                height: 48px;
                padding: 0 16px 16px;
                margin: 0 -22px;
                border-radius: 0 0 3px 3px;

                .item {
                    margin-bottom: 0;
                    height: 48px;

                    .action {
                        height: 100%;
                        padding: 10px 0;

                        &::after {
                            top: 0;
                        }

                        span {
                            padding: 6px 0;
                            text-transform: uppercase;
                            font-size: 16px;
                            line-height: 24px;
                            font-family: @cook-font-avenir-next-demi;
                        }
                    }
                }
            }
        }

        .page-wrapper {
            .cart-container {
                .cart-count-inner {
                    position: absolute;
                    width: 100%;
                    top: -45px;
                    text-align: right;
                }

                .details-container {
                    background: @color-white;
                    display: grid;

                    .image-wrapper {
                        grid-area: img;

                        .cart-image {
                            height: 110px;
                            width: 132px;
                            object-fit: cover;
                        }

                        img {
                            max-width: 132px;
                            border-radius: 3px;
                        }
                    }

                    .title-wrapper {
                        grid-area: title;
                        overflow: hidden;
                        padding-left: 16px;

                        a {
                            text-decoration: none;

                            &:hover,
                            &:active {
                                color: @color-cook-black;
                            }
                        }

                        p {
                            flex-grow: 1;
                            font-size: 16px;
                            font-family: @cook-font-avenir-next-demi;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            line-height: 24px;
                            height: 24px;
                            width: 100%;
                            -webkit-line-clamp: 1;
                            overflow: hidden;
                            word-break: break-all;
                            text-overflow: ellipsis;
                            margin-bottom: 16px;
                        }
                    }

                    .nr-wrapper {
                        grid-area: nr;
                        display: flex;
                        padding-left: 16px;

                        .detail {
                            p:first-child {
                                margin-block-end: 4px;
                            }

                            :last-child {
                                margin-bottom: 0;
                            }

                            .gtin-value {
                                overflow-wrap: break-word;
                            }

                            p {
                                color: @color-cook-black;
                                font-size: 14px;
                                line-height: 20px;

                                &:first-child {
                                    font-family: @cook-font-avenir-next-demi;
                                }

                                &:last-child {
                                    font-family: @cook-font-avenir-next-regular;
                                }

                                .tooltip-container {
                                    font-family: @cook-font-avenir-next-demi;;
                                    white-space: nowrap;
                                    position: relative;
                                    z-index: 1;

                                    img {
                                        width: 14px;
                                        height: 14px;
                                        max-width: unset;

                                        &:hover {
                                            & ~ .tooltip {
                                                display: block;
                                            }
                                        }
                                    }

                                    .tooltip {
                                        display: none;
                                        position: absolute;
                                        width: 200px;
                                        padding: 6px 12px;
                                        background-color: @color-light-blue;
                                        border: 1px solid @color-light-blue;
                                        text-align: center;
                                        text-transform: capitalize;
                                        top: -45px;
                                        left: -103px;

                                        &:after {
                                            content: '';
                                            position: absolute;
                                            bottom: -16px;
                                            left: 50%;
                                            -webkit-transform: translateX(-50%);
                                            -moz-transform: translateX(-50%);
                                            -ms-transform: translateX(-50%);
                                            -o-transform: translateX(-50%);
                                            transform: translateX(-50%);
                                            border-left: 8px solid transparent;
                                            border-right: 8px solid transparent;
                                            border-bottom: 8px solid transparent;
                                            border-top: 8px solid @color-light-blue;
                                        }
                                    }
                                }

                                .order-tooltip {
                                    .tooltip {
                                        top: -45px;
                                        left: -9px;
                                    }
                                }
                            }
                        }

                        .info-icon {
                            display: inline-block;
                            vertical-align: top;
                            margin-left: 2px;
                            margin-top: 1px;
                        }
                    }
                }

                .action-td {
                    background: @color-white;
                }

                .price-container {
                    white-space: nowrap;

                    .price {
                        margin-bottom: 0;
                        line-height: 24px;
                        font-size: 16px;
                    }

                    p:last-child {
                        font-size: 12px;
                        line-height: 18px;
                    }
                }

                .action-container {
                    .quantity-buttons-container {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;

                        .qty-actions {
                            display: flex;
                        }

                        .quantity-label {
                            align-self: flex-start;
                            font-size: 14px;
                            line-height: 20px;
                            margin-bottom: 4px;
                        }

                        .decrease-qty {
                            text-align: center;
                            margin: 0;
                        }

                        .quantity {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 36px;
                            height: 36px;
                            text-align: center;
                            border: 1px solid #E2E2E8;
                            border-radius: 3px;
                            margin: 0;
                            vertical-align: baseline;
                            padding: 0;

                            &:hover {
                                border-color: @color-cook-red;
                            }
                        }

                        .qty-value {
                            width: 48px;
                            height: 36px;
                            text-align: center;
                            font-size: 16px;
                            font-family: @cook-font-avenir-next-demi;
                            line-height: 24px;
                            border: none;
                            margin: 0;
                            padding: 0;
                            -moz-appearance: textfield;
                        }
                    }
                }

                .action-td.remove-td {
                    padding-right: 16px;
                    text-align: end;

                    .price {
                        padding: 6px 0;
                    }

                    .subtotal {
                        margin-bottom: 48px;
                    }
                }

                .remove-td {
                    .product.actions {
                        margin-left: -21px;

                        .primary {
                            a {
                                text-decoration: none;
                            }
                        }

                        .action.delete {
                            display: flex;
                            align-items: flex-start;
                            justify-content: flex-end;
                            gap: 8px;
                        }
                    }
                }
            }
        }
    }
}

// Desktop
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .checkout-cart-index {
        .page-wrapper {
            .cart-container {
                align-items: flex-start;

                .details-container {
                    border-radius: 3px 0 0 3px;
                    padding: 16px 0 16px 16px;
                    grid-template-areas:
                    'img title title'
                    'img nr nr';
                    width: 100%;
                    grid-template-columns: 132px 1fr min-content;
                }

                .mobile-icon {
                    display: none;
                }

                .action-td {
                    padding: 16px 0 16px 32px;

                    .price-container {
                        .price {
                            padding: 6px 0 4px 0;
                        }
                    }
                }

                .subtotal-container {
                    padding-top: 6px;

                    .price-excluding-tax,
                    .subtotal {
                        line-height: 24px;
                        font-size: 16px;
                        font-family: @cook-font-avenir-next-demi;
                    }
                }

                .image-wrapper {
                    width: 132px;
                    height: 110px;
                }

                .title-wrapper {
                    min-width: 336px;
                    padding-left: 16px;

                    p {
                        padding: 5px 0;

                        &:before,
                        &:after {
                            content: "";
                            height: 1px;
                            display: block;
                        }
                    }
                }

                .quantity-label {
                    margin-bottom: 4px;
                }

                .nr-wrapper {
                    gap: 24px;
                    padding-left: 16px;
                }

                .form-cart {
                    width: 64.91%;
                    padding-right: 0;

                    .remove-td {
                        border-radius: 0 3px 3px 0;
                    }

                    .uom-td {
                        border-radius: 0 0 0 3px;
                    }

                    .cart.table-wrapper .cart thead tr th.col {
                        border-bottom: 2px solid @color-cook-light-gray;
                    }

                    .cart.table-wrapper .cart > .item {
                        position: relative;
                        border-bottom: none;

                        &:after {
                            content: "";
                            position: absolute;
                            left: 0;
                            bottom: 0;
                            width: 100%;
                            height: 2px;
                            background: @color-cook-light-gray;
                        }
                    }

                    thead {
                        .col {
                            white-space: nowrap;
                            background: @color-white;
                            font-size: 16px;
                            padding: 12px 0 12px 32px;
                            text-align: justify;
                            font-family: @cook-font-avenir-next-demi;
                            line-height: 24px;
                            font-weight: 400;

                            span {
                                padding-top: 2px;
                            }
                        }

                        .item {
                            padding-left: 16px;
                            border-radius: 3px 0 0 3px;
                        }

                        .price {
                            padding-left: 20px;
                            text-align: center;
                        }

                        .subtotal {
                            padding-right: 16px;
                            border-radius: 0 3px 3px 0;
                            padding-left: 0;
                            text-align: end;
                        }
                    }

                    .action-remove {
                        font-family: @cook-font-avenir-next-demi;
                        font-size: 16px;
                        line-height: 24px;
                    }
                }

                .cart-summary {
                    padding-top: 0;
                    width: 32.1%;

                    .cart-totals {
                        padding-top: 0;
                    }

                    .summary.title {
                        position: absolute;
                        top: -54px;
                        margin: 0 -22px;
                        font-family: @cook-font-avenir-next-demi;
                        font-size: 28px;
                        line-height: 38px;
                        background-color: @color-cook-light-gray;
                        padding-bottom: 16px;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 1335px) {
    .checkout-cart-index {
        .page-wrapper {
            .cart-container {
                .cart.items {
                    tr {
                        display: flex;
                        flex-wrap: wrap;
                    }

                    td:last-of-type {
                        justify-content: end;
                    }

                    .action-td {
                        flex: 1;
                        display: flex;
                        align-items: flex-end;
                        padding: 0 0 20px 0;
                    }

                    .price-container {
                        padding: 0 0 0 16px;

                        .quantity-label {
                            margin: 0;
                        }

                        .price {
                            padding: 0;
                        }
                    }

                    .action-td.remove-td {
                        display: flex;
                        align-items: center;
                        padding: 0 16px 20px 0;
                        border-radius: 0 0 3px 0;

                        .subtotal {
                            display: inline-block;
                            margin: 0;

                            .price {
                                padding: 0;
                            }
                        }
                    }

                    .action-td.action-container {
                        justify-content: center;
                        align-items: center
                    }
                }

                .cart.table-wrapper thead .col.item,
                .cart.table-wrapper thead .col.qty,
                .cart.table-wrapper thead .col.price,
                .cart.table-wrapper thead .col.subtotal,
                .cart.table-wrapper thead .col.msrp {
                    display: none;
                }

                .details-container {
                    border-radius: 3px 3px 0 0;
                    padding-right: 16px;
                    width: 100%;
                    grid-template-areas:
                    'img title title'
                    'img nr nr';
                    grid-template-columns: 132px 1fr min-content;

                    .title-wrapper {
                        display: flex;

                        a, p {
                            width: 100%;
                        }
                    }

                    .mobile-icon {
                        display: block;
                        flex: 0 0 24px;
                        padding: 6px 0 0 16px;
                        height: 100%;
                        text-align: end;

                        .action-remove {
                            display: none;
                        }
                    }
                }

                .nr-wrapper {
                    flex: 1 0 100%;
                    justify-content: space-between;
                }

                .product.actions {
                    display: none;
                }
            }
        }
    }
}

// Mobile
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__l) {
    .checkout-cart-index {
        .page-wrapper {
            .cart.table-wrapper .cart thead tr th.col {
                border-bottom: 2px solid @color-cook-light-gray;
            }

            .cart.table-wrapper .cart > .item {
                border-bottom: 2px solid @color-cook-light-gray;
            }

            .cart.table-wrapper .cart > .item:last-of-type {
                border: unset;
            }

            .cart-container {
                .details-container {
                    padding: 16px 0 16px 16px;
                    grid-template-areas:
                    'img img title title'
                    'img img nr nr';
                }

                .title-wrapper {
                    p {
                        padding: 5px 0;

                        &:before,
                        &:after {
                            content: "";
                            height: 1px;
                            display: block;
                        }
                    }
                }

                .nr-wrapper {
                    gap: 12px;
                }

                .form-cart {
                    width: 64.91%;
                    padding-right: 0;

                    .uom-td {
                        border-radius: 0 0 0 3px;
                    }

                    .remove-td {
                        border-radius: unset;
                    }
                }

                .cart-summary {
                    background: @color-white;
                    padding-top: 0;
                    width: 32.1%;

                    .cart-totals {
                        padding-top: 0;
                    }

                    .summary.title {
                        position: absolute;
                        top: -54px;
                        margin: 0 -22px;
                        font-family: @cook-font-avenir-next-demi;
                        font-size: 28px;
                        line-height: 38px;
                        background-color: @color-cook-light-gray;
                        padding-bottom: 16px;

                    }

                    .grand {
                        .amount {
                            border-radius: unset;
                        }

                        .mark {
                            border-radius: unset;
                        }
                    }

                    .checkout-methods-items {
                        .checkout {
                            span {
                                line-height: 28px;
                            }
                        }
                    }
                }
            }

            .price-excluding-tax,
            .subtotal {
                line-height: 24px;
                font-size: 16px;
                font-family: @cook-font-avenir-next-demi;
            }

            .action-td {
                padding: 16px 0 16px 32px;

                .price-container {
                    .price {
                        font-size: 16px;
                        font-weight: 400;
                        font-family: @cook-font-avenir-next-regular;
                    }
                }
            }
        }
    }
}

// Mobile
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .checkout-cart-index {

        .add-more-products {
            margin-left: 16px !important;
        }

        .page-wrapper {
            .page-main {
                .page-title-wrapper {
                    display: flex;
                    align-items: baseline;
                    padding-top: 0;

                    .page-title {
                        padding-top: 32px;
                        flex: 1 1 auto;
                        line-height: 32px;

                        .base {
                            line-height: 32px;
                        }
                    }

                    .cart-count {
                        display: block;
                        line-height: 24px;
                        margin-bottom: 0px;
                    }
                }
            }

            .cart-container {
                display: flex;
                flex-direction: column;

                .form-cart {
                    width: 100%;
                    padding-right: 0;
                    margin: 0;

                    .table-wrapper {
                        border-top: none;

                        .cart {
                            border-bottom: 2px solid @color-cook-light-gray;
                        }
                    }

                    .action-td.remove-td {
                        display: flex;
                        border-radius: unset;
                        align-items: center;
                        padding: 0 16px 20px 0;

                        .subtotal {
                            display: inline-block;
                            margin: 0;
                        }

                        .price {
                            padding: 0;
                            line-height: 24px;
                            font-size: 16px;
                            font-family: @cook-font-avenir-next-demi;
                        }
                    }

                    thead {
                        .item {
                            border-radius: unset;
                        }

                        .subtotal {
                            border-radius: unset;
                        }
                    }

                    .details-container {
                        padding: 16px 16px 0 16px;
                        border-radius: unset;
                        grid-template-areas:
                        'img title title'
                        'nr nr nr';
                        width: 100%;
                        grid-template-columns: 100px 1fr 1fr;

                        .image-wrapper {
                            height: 100px;

                            .cart-image {
                                height: 100px;
                                width: 100px;
                                border-radius: unset;
                            }
                        }

                        .title-wrapper {
                            display: flex;
                            padding-left: 12px;

                            p {
                                padding: 0;

                                &:before,
                                &:after {
                                    content: none;
                                }
                            }

                            .mobile-icon {
                                display: block;
                                flex: 0 0 24px;
                                padding: 0 0 0 16px;
                                height: 100%;
                                text-align: end;

                                .action-remove {
                                    display: none;
                                }
                            }
                        }
                    }

                    div.nr-wrapper {
                        margin: 16px 0;
                        padding-left: 0;
                        align-items: baseline;
                        gap: 18px;
                        justify-content: space-between;

                        .detail {
                            display: flex;
                            justify-content: flex-end;
                            flex-direction: column;

                            p:first-child {
                                margin-block-end: 4px;
                            }
                        }

                        .detail:last-child {
                            .tooltip-container {
                                .tooltip {
                                    top: -45px;
                                    left: -133px;

                                    &:after {
                                        left: 64%;
                                    }
                                }
                            }
                        }
                    }
                }

                .cart-summary {
                    width: 100%;
                    margin: 0;
                    float: none;
                    position: fixed;
                    z-index: 55;
                    bottom: 0;
                    border-radius: unset;
                    padding: 0;
                    border-top: 2px solid @color-cook-borders-gray;

                    .table-wrapper {
                        margin: 0;
                    }

                    .sub {
                        .amount {
                            border-radius: unset;
                        }

                        .mark {
                            border-radius: unset;
                        }
                    }

                    .checkout-methods-items {
                        border-radius: unset;
                        margin: 0;

                        .checkout {
                            line-height: 24px;
                        }
                    }

                    .cart-totals {
                        padding: 0 1px;
                    }

                    .title {
                        display: none;
                    }

                    .totals {
                        .sub {
                            display: none;
                        }

                        .amount,
                        .mark {
                            padding-top: 16px;
                        }
                    }
                }

                .cart.items {
                    tr {
                        display: flex;
                        flex-wrap: wrap;

                        td:last-of-type {
                            justify-content: end;
                        }

                        .uom-td {
                            border-radius: unset;
                        }

                        .action-td {
                            flex: 1 1 auto;
                            display: flex;
                            align-items: flex-end;
                            padding: 0 0 20px 0;
                        }

                        .price-container {
                            padding: 3px 0 3px 16px;

                            .quantity-label {
                                margin: 0;
                            }

                            .price {
                                padding: 0;
                            }
                        }

                        .action-td.remove-td {
                            border-radius: unset;
                        }

                        .action-td.action-container {
                            justify-content: center;
                            align-items: center;

                            .qty-value,
                            .quantity {
                                width: 48px;
                                height: 48px;
                            }
                        }
                    }
                }
            }
        }
    }
}
