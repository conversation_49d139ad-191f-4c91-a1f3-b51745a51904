/**
 * @category Cook Medical
 * @package frontend/CookMedical/Magento_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Ltd (http://scandiweb.com)
 */

@import '_shipping.less';
@import "_cart_template.less";
@import '_checkout';
@import '_cart';
@import '_checkout_success';

//  Common
& when (@media-common = true) {
    .block.block-minicart {
        .minicart-header-wrapper {
            display: flex;
            justify-content: space-between;
            padding: 16px;

            .my-cart-items .title {
                margin: 0;
                font-family: @cook-font-avenir-next-demi;
                font-weight: 600;
                font-size: 20px;
                line-height: 24px;
            }

            .items-total {
                font-size: 16px;
                font-family: @cook-font-avenir-next-regular;
                font-weight: 400;
                line-height: 24px;
                margin: 0;
            }
        }

        .product-item-top .actions {
            padding: 2px 1px;
        }

        .actions {
            display: flex;
            flex-direction: column;
            padding: 16px 24px;

            .subtotal {
                display: flex;
                justify-content: space-between;
                margin: 0 0 12px 0;

                .label {
                    font-weight: 400;
                    font-size: 16px;

                    &:after {
                        content: none;
                    }
                }

                .price-container {
                    font-size: 20px;
                    font-family: @cook-font-avenir-next-demi;
                    font-weight: 600;
                }
            }

            .bottom-action-buttons {
                display: flex;
                align-items: center;
                gap: 8px;

                .secondary {
                    border: 1px solid @color-cook-red;
                    border-radius: 3px;
                    height: 46px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-basis: 50%;
                    flex-grow: 0;
                    text-transform: uppercase;

                    &:hover {
                        border-color: @color-cook-burgundy;
                    }

                    .action {
                        text-decoration: none;
                        text-transform: uppercase;
                        font-size: @font-size__base;
                        font-family: @cook-font-avenir-next-demi;
                        font-weight: 600;
                    }
                }

                .actions {
                    padding: 0;
                    flex-basis: 50%;
                    flex-grow: 0;

                    .action.primary {
                        text-transform: uppercase;
                        width: 100%;
                    }
                }
            }
        }

        #minicart-widgets {
            margin: 0;
        }
    }

    .page-header {
        &:has(div.minicart-wrapper.active) {
            z-index: 4;

            &::before {
                content: '';
                display: block;
                position: fixed;
                inset: 0;
                background-color: rgba(0, 0, 0, 0.7);
            }

            .header.content {
                background-color: white;
            }
        }
    }

    div.minicart-wrapper div.block-minicart {
        padding: 0;
        border: 1px solid @color-cook-borders-gray;
        border-radius: 3px;
        width: 450px;
        overflow: visible;
        margin-top: 9px;

        &::before {
            border-color: transparent transparent @color-cook-borders-gray transparent;
        }

        div.block-content {
            max-height: 80vh;
            display: flex;
            flex-direction: column;

            .block-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;

                strong span.text {
                    font-size: 20px;
                    line-height: 24px;
                }

                .action.close {
                    position: static;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 24px;
                    height: 24px;

                    &::before {
                        color: @color-cook-black;
                        content: url('../Magento_Checkout/icon/close.svg');
                        width: 12px;
                        height: 12px;
                    }
                }
            }

            .minicart-items-wrapper {
                margin: 0;
                padding: 0;
                border-color: @color-cook-borders-gray;
                border-style: solid;
                border-top-width: 2px;
                border-left-width: 0;
                border-right-width: 0;
                border-bottom: 2px solid @color-cook-borders-gray;

                .minicart-items {
                    li.product-item {
                        padding: 16px 16px 20px;

                        &:not(:first-child) {
                            border-top: 2px solid @color-cook-light-gray;
                        }

                        .product-item {
                            padding: 0;
                            display: flex;
                            flex-direction: column;
                            gap: 16px;

                            &::before, &::after {
                                display: none;
                            }

                            div.product-item-top {
                                display: flex;
                                gap: 12px;

                                .product-item-photo{
                                    width: 100px;
                                    height: 100px;
                                    overflow: hidden;
                                    flex-shrink: 0;

                                    .product-image-container {
                                        width: auto !important;
                                    }

                                    img.product-image-photo {
                                        height: 100px !important;
                                        width: 100px !important;
                                        overflow: hidden;
                                    }
                                }

                                strong.product-item-name {
                                    overflow: hidden;
                                    flex-grow: 1;
                                    text-overflow: ellipsis;
                                    display: -webkit-box;
                                    line-height: 24px;
                                    max-height: 48px;
                                    -webkit-line-clamp: 2;
                                    -webkit-box-orient: vertical;
                                    text-transform: uppercase;
                                    font-weight: 600;
                                    font-family: @cook-font-avenir-next-demi;

                                    & > a {
                                        display: block;
                                        width: 100%;
                                        height: 100%;
                                    }
                                }

                                div.product.actions {
                                    float: none;
                                    margin: 0 0 0 4px;

                                    .action.delete {
                                        &::before {
                                            content: url('../images/trash.svg');
                                        }
                                    }
                                }
                            }

                            div.product-item-details {
                                display: flex;
                                justify-content: space-between;
                                padding: 0;
                                gap: 10px;

                                & > div {
                                    display: flex;
                                    flex-direction: column;
                                    gap: 4px;
                                }

                                > span {
                                    font-size: 14px;
                                    font-weight: 400;
                                    line-height: 20px;
                                    font-family: @cook-font-avenir-next-regular;
                                }

                                > div {
                                    font-size: 14px;
                                }

                                .detail-title {
                                    min-width: max-content;

                                    strong {
                                        font-size: 14px;
                                        font-weight: 600;
                                        font-family: @cook-font-avenir-next-demi;
                                        line-height: 20px;
                                    }

                                    .info-icon {
                                        position: relative;

                                        &:hover {
                                            .tooltip-container {
                                                display: block;
                                                background-color: @color-light-blue;
                                                padding: 8px 12px;
                                                font-weight: 600;
                                                font-family: @cook-font-avenir-next-demi;
                                                font-size: 14px;
                                                white-space: nowrap;
                                                top: -39px;
                                                right: -70px;
                                                z-index: 50;
                                                text-transform: capitalize;

                                                &.order-number {
                                                    right: unset;
                                                }
                                            }

                                            &::after {
                                                content: '';
                                                position: absolute;
                                                bottom: 10px;
                                                left: 50%;
                                                -webkit-transform: translateX(-50%);
                                                -moz-transform: translateX(-50%);
                                                -ms-transform: translateX(-50%);
                                                -o-transform: translateX(-50%);
                                                transform: translateX(-50%);
                                                border-left: 8px solid transparent;
                                                border-right: 8px solid transparent;
                                                border-bottom: 8px solid transparent;
                                                border-top: 8px solid #d8e5ef;
                                            }
                                        }

                                        .tooltip-container {
                                            position: absolute;
                                            display: none;
                                        }
                                    }
                                }
                            }

                            div.product-item-pricing {
                                display: flex;
                                align-items: center;
                                text-wrap: nowrap;

                                div.details-qty {
                                    display: flex;
                                    align-items: center;

                                    button {
                                        border-color: @color-cook-borders-gray;

                                        &:hover {
                                            border-color: @color-cook-red;
                                        }
                                    }

                                    .change-item-qty {
                                        width: 48px;
                                        height: 48px;
                                        padding: 0;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;

                                        &:disabled {
                                            background-color: @color-white;
                                        }

                                        span.increase-icon, span.decrease-icon {
                                            width: 24px;
                                            height: 24px;
                                        }
                                    }

                                    .cart-item-qty-display {
                                        display: block;
                                        text-align: center;
                                        font-size: 16px;
                                        font-family: @cook-font-avenir-next-demi;
                                        font-weight: 600;
                                        width: 48px;
                                    }
                                }

                                div.price-each, div.price-container {
                                    flex-basis: 0;
                                    flex-grow: 1;
                                    height: min-content;
                                }

                                div.price-container {
                                    span.price {
                                        font-size: 16px;
                                        line-height: 24px;
                                    }
                                }

                                span.UOM {
                                    font-size: 12px;
                                    line-height: 18px;
                                    display: block;
                                }

                                & > div.price-container {
                                    display: flex;
                                    justify-content: flex-end;
                                    align-items: center;
                                    font-family: 'AvenirNextDemi', Arial, sans-serif;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// Large desktop
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {
    .page-header {
        &:has(div.minicart-wrapper.active) {
            .header.content {
                padding: 16px ~"calc((100vw - 1440px) / 2 + 52px)";
                margin-inline: ~"calc(-1*(100vw - 1440px) / 2)";
                max-width: unset;
            }
        }
    }
}

// Mobile
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .block.block-minicart {
        display: none !important;
    }

    .page-header {
        &:has(div.minicart-wrapper.active) {
            &::before {
                content: none !important;
            }
        }
    }
}
