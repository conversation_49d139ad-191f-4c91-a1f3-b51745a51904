define([
    'jquery',
    'underscore',
    'Magento_Ui/js/form/form',
    'ko',
    'Magento_Customer/js/model/customer',
    'Magento_Customer/js/model/address-list',
    'Magento_Checkout/js/model/address-converter',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/action/create-shipping-address',
    'Magento_Checkout/js/action/select-shipping-address',
    'Magento_Checkout/js/model/shipping-rates-validator',
    'Magento_Checkout/js/model/shipping-address/form-popup-state',
    'Magento_Checkout/js/model/shipping-service',
    'Magento_Checkout/js/action/select-shipping-method',
    'Magento_Checkout/js/model/shipping-rate-registry',
    'Magento_Checkout/js/action/set-shipping-information',
    'Magento_Checkout/js/model/step-navigator',
    'Magento_Ui/js/modal/modal',
    'Magento_Checkout/js/model/checkout-data-resolver',
    'Magento_Checkout/js/checkout-data',
    'uiRegistry',
    'mage/translate',
    'Magento_Ui/js/model/messageList',
    'Magento_Checkout/js/model/shipping-rate-service'
], function (
    $,
    _,
    Component,
    ko,
    customer,
    addressList,
    addressConverter,
    quote,
    createShippingAddress,
    selectShippingAddress,
    shippingRatesValidator,
    formPopUpState,
    shippingService,
    selectShippingMethodAction,
    rateRegistry,
    setShippingInformationAction,
    stepNavigator,
    modal,
    checkoutDataResolver,
    checkoutData,
    registry,
    $t,
    globalMessageList
) {
    'use strict';

    var popUp = null;

    return Component.extend({
        defaults: {
            template: 'Magento_Checkout/shipping',
            shippingFormTemplate: 'Magento_Checkout/shipping-address/form',
            shippingMethodListTemplate: 'Magento_Checkout/shipping-address/shipping-method-list',
            shippingMethodItemTemplate: 'Magento_Checkout/shipping-address/shipping-method-item',
            imports: {
                countryOptions: '${ $.parentName }.shippingAddress.shipping-address-fieldset.country_id:indexedOptions'
            }
        },
        visible: ko.observable(!quote.isVirtual()),
        errorValidationMessage: ko.observable(false),
        isCustomerLoggedIn: customer.isLoggedIn,
        isFormPopUpVisible: formPopUpState.isVisible,
        isFormInline: addressList().length === 0,
        isNewAddressAdded: ko.observable(false),
        saveInAddressBook: 1,
        quoteIsVirtual: quote.isVirtual(),

        /**
         * @return {exports}
         */
        initialize: function () {
            var self = this,
                hasNewAddress,
                fieldsetName = 'checkout.steps.shipping-step.shippingAddress.shipping-address-fieldset';

            this.courierNumber = ko.observable('').extend({ required: true });
            this.recipientName = ko.observable('').extend({ required: true });
            this.recipientPhone = ko.observable('').extend({ required: true });

            this.setupPhoneFormatting();

            this._super();

            if (!quote.isVirtual()) {
                stepNavigator.registerStep(
                    'shipping',
                    '',
                    $t('Contact & Shipping'),
                    this.visible, _.bind(this.navigate, this),
                    this.sortOrder
                );
            }
            checkoutDataResolver.resolveShippingAddress();

            hasNewAddress = addressList.some(function (address) {
                return address.getType() == 'new-customer-address'; //eslint-disable-line eqeqeq
            });

            this.isNewAddressAdded(hasNewAddress);

            this.isFormPopUpVisible.subscribe(function (value) {
                if (value) {
                    self.getPopUp().openModal();
                }
            });

            quote.shippingMethod.subscribe(function () {
                self.errorValidationMessage(false);
            });

            registry.async('checkoutProvider')(function (checkoutProvider) {
                var shippingAddressData = checkoutData.getShippingAddressFromData();

                if (shippingAddressData) {
                    checkoutProvider.set(
                        'shippingAddress',
                        $.extend(true, {}, checkoutProvider.get('shippingAddress'), shippingAddressData)
                    );
                }
                checkoutProvider.on('shippingAddress', function (shippingAddrsData, changes) {
                    var isStreetAddressDeleted, isStreetAddressNotEmpty;

                    /**
                     * In last modifying operation street address was deleted.
                     * @return {Boolean}
                     */
                    isStreetAddressDeleted = function () {
                        var change;

                        if (!changes || changes.length === 0) {
                            return false;
                        }

                        change = changes.pop();

                        if (_.isUndefined(change.value) || _.isUndefined(change.oldValue)) {
                            return false;
                        }

                        if (!change.path.startsWith('shippingAddress.street')) {
                            return false;
                        }

                        return change.value.length === 0 && change.oldValue.length > 0;
                    };

                    isStreetAddressNotEmpty = shippingAddrsData.street && !_.isEmpty(shippingAddrsData.street[0]);

                    if (isStreetAddressNotEmpty || isStreetAddressDeleted()) {
                        checkoutData.setShippingAddressFromData(shippingAddrsData);
                    }
                });
                shippingRatesValidator.initFields(fieldsetName);
            });

            $(document).ready(function() {
                $(document).on('change', '.method-row .radio', function() {
                    $('.method-row').removeClass('active-method');
                    $(this).closest('.method-row').addClass('active-method');
                });

                $('.method-row .radio:checked').trigger('change');
            });

            return this;
        },

        setupPhoneFormatting: function () {
            var self = this;

            $(document).on('input', '.recipient-phone', function (event) {
                let input = event.target;
                let value = input.value.replace(/[^+\d]/g, '');

                if (value === '' || value[0] !== '+') {
                    value = '+' + value;
                }
                if (value.length > 15) {
                    value = value.substring(0, 15);
                }

                input.value = value;
                self.recipientPhone(value);
            });
        },

        applyActiveMethodClass: function () {
            $('.method-row').removeClass('active-method');
            $(this).closest('.method-row').addClass('active-method');
            $('.method-row .radio:checked').trigger('change');
        },

        /**
         * Navigator change hash handler.
         *
         * @param {Object} step - navigation step
         */
        navigate: function (step) {
            step && step.isVisible(true);
        },

        /**
         * @return {*}
         */
        getPopUp: function () {
            var self = this,
                buttons;

            if (!popUp) {
                buttons = this.popUpForm.options.buttons;
                this.popUpForm.options.buttons = [
                    {
                        text: buttons.save.text ? buttons.save.text : $t('Save Address'),
                        class: buttons.save.class ? buttons.save.class : 'action primary action-save-address',
                        click: self.saveNewAddress.bind(self)
                    },
                    {
                        text: buttons.cancel.text ? buttons.cancel.text : $t('Cancel'),
                        class: buttons.cancel.class ? buttons.cancel.class : 'action secondary action-hide-popup',

                        /** @inheritdoc */
                        click: this.onClosePopUp.bind(this)
                    }
                ];

                /** @inheritdoc */
                this.popUpForm.options.closed = function () {
                    self.isFormPopUpVisible(false);
                };

                this.popUpForm.options.modalCloseBtnHandler = this.onClosePopUp.bind(this);
                this.popUpForm.options.keyEventHandlers = {
                    escapeKey: this.onClosePopUp.bind(this)
                };

                /** @inheritdoc */
                this.popUpForm.options.opened = function () {
                    // Store temporary address for revert action in case when user click cancel action
                    self.temporaryAddress = $.extend(true, {}, checkoutData.getShippingAddressFromData());
                };
                popUp = modal(this.popUpForm.options, $(this.popUpForm.element));
            }

            return popUp;
        },

        /**
         * Revert address and close modal.
         */
        onClosePopUp: function () {
            checkoutData.setShippingAddressFromData($.extend(true, {}, this.temporaryAddress));
            this.getPopUp().closeModal();
        },

        /**
         * Show address form popup
         */
        showFormPopUp: function () {
            this.isFormPopUpVisible(true);
        },

        /**
         * Save new shipping address
         */
        saveNewAddress: function () {
            var addressData,
                newShippingAddress;

            this.source.set('params.invalid', false);
            this.triggerShippingDataValidateEvent();

            if (!this.source.get('params.invalid')) {
                addressData = this.source.get('shippingAddress');
                // if user clicked the checkbox, its value is true or false. Need to convert.
                addressData['save_in_address_book'] = this.saveInAddressBook ? 1 : 0;

                // New address must be selected as a shipping address
                newShippingAddress = createShippingAddress(addressData);
                selectShippingAddress(newShippingAddress);
                checkoutData.setSelectedShippingAddress(newShippingAddress.getKey());
                checkoutData.setNewCustomerShippingAddress($.extend(true, {}, addressData));
                this.getPopUp().closeModal();
                this.isNewAddressAdded(true);
            }
        },

        /**
         * Shipping Method View
         */
        rates: shippingService.getShippingRates(),
        isLoading: shippingService.isLoading,
        isSelected: ko.computed(function () {
            return quote.shippingMethod() ?
                quote.shippingMethod()['carrier_code'] + '_' + quote.shippingMethod()['method_code'] :
                null;
        }),

        /**
         * @param {Object} shippingMethod
         * @return {Boolean}
         */
        selectShippingMethod: function (shippingMethod) {
            selectShippingMethodAction(shippingMethod);
            checkoutData.setSelectedShippingRate(shippingMethod['carrier_code'] + '_' + shippingMethod['method_code']);
            $('.method-row').removeClass('active-method');
            $(`[value="${shippingMethod['carrier_code']}_${shippingMethod['method_code']}"]`).closest('.method-row').addClass('active-method');

            // Trigger real-time shipping cost update in sidebar
            this.triggerShippingCostUpdate(shippingMethod);

            return true;
        },

        /**
         * Trigger shipping cost update in sidebar
         * @param {Object} shippingMethod
         */
        triggerShippingCostUpdate: function (shippingMethod) {
            // Use the dedicated shipping cost updater for real-time updates
            require([
                'CookMedical_Checkout/js/shipping-cost-updater'
            ], function (shippingCostUpdater) {
                shippingCostUpdater.updateShippingCost(shippingMethod);
            });
        },

        /**
         * Set shipping information handler
         */
        setShippingInformation: function () {
            const shippingAddress = quote.shippingAddress();
            const shippingCustomAttributes = shippingAddress?.customAttributes;
            const shipCookAccountCode = shippingCustomAttributes.find((attr) => attr.attribute_code === 'cook_account_code')?.value;
            const quoteSubtotal = quote.totals()['subtotal'];

            $.ajax({
                type: 'POST',
                url: '/snaplogic/account/accountcheck',
                showLoader: true,
                global: false,
                data: {
                    customer_ship_account: shipCookAccountCode,
                    sales_value: quoteSubtotal
                },
                success: $.proxy(function (data) {
                    const responseData = JSON.parse(data);

                    if (responseData?.success === true) {
                        if (this.validateShippingInformation()) {
                            quote.billingAddress(null);
                            checkoutDataResolver.resolveBillingAddress();
                            registry.async('checkoutProvider')(function (checkoutProvider) {
                                var shippingAddressData = checkoutData.getShippingAddressFromData();

                                if (shippingAddressData) {
                                    checkoutProvider.set(
                                        'shippingAddress',
                                        $.extend(true, {}, checkoutProvider.get('shippingAddress'), shippingAddressData)
                                    );
                                }
                            });
                            setShippingInformationAction().done(
                                function () {
                                    stepNavigator.next();
                                }
                            );
                        }
                    } else {
                        globalMessageList.addErrorMessage({
                            message: responseData.message || $t('Something went wrong')
                        });
                    }
                }, this),
                error: function (jqXHR, _textStatus, errorThrown) {
                    console.error(jqXHR.responseText);
                    globalMessageList.addErrorMessage({
                        message: errorThrown || $t('Something went wrong')
                    });
                }
            });
        },

        /**
         * @return {Boolean}
         */
        validateShippingInformation: function () {
            var shippingAddress,
                addressData,
                loginFormSelector = 'form[data-role=email-with-possible-login]',
                emailValidationResult = customer.isLoggedIn(),
                field,
                option = _.isObject(this.countryOptions) && this.countryOptions[quote.shippingAddress().countryId],
                messageContainer = registry.get('checkout.errors').messageContainer;

            if (!quote.shippingMethod()) {
                this.errorValidationMessage(
                    $t('The shipping method is missing. Select the shipping method and try again.')
                );

                return false;
            }

            if (!customer.isLoggedIn()) {
                $(loginFormSelector).validation();
                emailValidationResult = Boolean($(loginFormSelector + ' input[name=username]').valid());
            }

            if (this.isFormInline) {
                this.source.set('params.invalid', false);
                this.triggerShippingDataValidateEvent();

                if (!quote.shippingMethod()['method_code']) {
                    this.errorValidationMessage(
                        $t('The shipping method is missing. Select the shipping method and try again.')
                    );
                }

                if (emailValidationResult &&
                    this.source.get('params.invalid') ||
                    !quote.shippingMethod()['method_code'] ||
                    !quote.shippingMethod()['carrier_code']
                ) {
                    this.focusInvalid();

                    return false;
                }

                shippingAddress = quote.shippingAddress();
                addressData = addressConverter.formAddressDataToQuoteAddress(
                    this.source.get('shippingAddress')
                );

                //Copy form data to quote shipping address object
                for (field in addressData) {
                    if (addressData.hasOwnProperty(field) &&  //eslint-disable-line max-depth
                        shippingAddress.hasOwnProperty(field) &&
                        typeof addressData[field] != 'function' &&
                        _.isEqual(shippingAddress[field], addressData[field])
                    ) {
                        shippingAddress[field] = addressData[field];
                    } else if (typeof addressData[field] != 'function' &&
                        !_.isEqual(shippingAddress[field], addressData[field])) {
                        shippingAddress = addressData;
                        break;
                    }
                }

                if (customer.isLoggedIn()) {
                    shippingAddress['save_in_address_book'] = 1;
                }
                selectShippingAddress(shippingAddress);
            } else if (customer.isLoggedIn() &&
                option &&
                option['is_region_required'] &&
                !quote.shippingAddress().region
            ) {
                messageContainer.addErrorMessage({
                    message: $t('Please specify a regionId in shipping address.')
                });

                return false;
            }

            if (!emailValidationResult) {
                $(loginFormSelector + ' input[name=username]').trigger('focus');

                return false;
            }

            const selectedMethod = this.rates().find(
                method => method.carrier_code + '_' + method.method_code === this.isSelected()
            );

            if (!selectedMethod) {
                this.errorValidationMessage($t('The shipping method is missing. Select the shipping method and try again.'));
                return false;
            }

            let isValid = true;
            var firstInvalidField = null;

            if (selectedMethod.courier_nbr_flag && (!selectedMethod.courierNumber || selectedMethod.courierNumber.trim() === '')) {
                this.errorValidationMessage($t('Courier Number is required.'));
                isValid = false;

                if (!firstInvalidField) {
                    firstInvalidField = $('.courier-number:visible');
                }
            }

            if (selectedMethod.shipvia_recipient_flag && (!selectedMethod.recipientName || selectedMethod.recipientName.trim() === '')) {
                this.errorValidationMessage($t('Recipient Name is required.'));
                isValid = false;

                if (!firstInvalidField) {
                    firstInvalidField = $('.recipient-name:visible');
                }
            }

            if (selectedMethod.shipvia_recipient_flag && (!selectedMethod.recipientPhone || selectedMethod.recipientPhone.trim() === '')) {
                this.errorValidationMessage($t('Recipient Phone is required.'));
                isValid = false;

                if (!firstInvalidField) {
                    firstInvalidField = $('.recipient-phone:visible');
                }
            }

            if (!isValid && firstInvalidField) {
                firstInvalidField.focus();
                return isValid;
            }

            return true;
        },

        /**
         * Trigger Shipping data Validate Event.
         */
        triggerShippingDataValidateEvent: function () {
            this.source.trigger('shippingAddress.data.validate');

            if (this.source.get('shippingAddress.custom_attributes')) {
                this.source.trigger('shippingAddress.custom_attributes.data.validate');
            }
        }
    });
});
