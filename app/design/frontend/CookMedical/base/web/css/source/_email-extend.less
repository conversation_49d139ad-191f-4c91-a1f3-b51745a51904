#email-body {
    color: #666;
    background-color: #f9f9f9;
    font-family: 'Avenir', Arial, sans-serif;
    font-size: 16px;
    font-weight: 500;
    -webkit-font-smoothing: antialiased;
    width: 100%;
    line-height: 1.4;
    margin: 0;
    padding: 0;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;

    img {
        border: none;
        -ms-interpolation-mode: bicubic;
        max-width: 100%;
    }

    h1, h2, h3, h4, strong {
        color: #555;
    }

    .payment p {
        margin-bottom: 0 !important;
    }

    dl {
        margin: -20px 0 0 !important;

        dt {
            font-weight: normal;
            margin-bottom: 0;
        }
        dd {
            padding: 0 !important;

            caption {
                display: none;
            }
        }
    }

    table {
        border-collapse: separate;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
        width: 100%;

        td {
            font-size: 16px;
            vertical-align: top;
            font-family: 'Avenir', Arial, sans-serif;
        }
    }

    .container {
        display: block;
        margin: 0 auto !important;
        max-width: 680px;
        padding: 10px;
        width: 680px;
    }

    .content {
        box-sizing: border-box;
        display: block;
        margin: 0 auto;
        max-width: 680px;
        padding: 10px;
    }

    .main {
        background: #ffffff;
        border: 1px solid #e6e6e6;
        width: 100%;
        border-radius: 6px;
    }

    .wrapper {
        box-sizing: border-box;
        padding: 20px;
    }

    .content-block {
        padding: 10px 0;
    }

    .footer {
        clear: both;
        box-sizing: border-box;
        width: 100%;

        td, p, span {
            color: #999999;
            font-size: 14px;
        }

        td {
            padding: 15px 20px;
        }
    }

    p, ul, ol {
        margin: 0;
        margin-bottom: 20px;
    }

    p {
        color: #666;

        li, ul li, ol li {
            list-style-position: inside;
            margin-left: 5px;
        }
    }

    a {
        color: #3f748d;
        text-decoration: underline !important;

        &:hover {
            color: #224d61;
        }
    }

    .btn {
        box-sizing: border-box;
        width: 100%;

        table {
            width: auto;
            max-width: 300px;

            td {
                background-color: #91A060;
                border-radius: 4px;
                text-align: center;
            }
        }

        a {
            cursor: pointer;
            margin: 0;
            box-sizing: border-box;
            border-radius: 4px;
            padding: 8px 24px;
            color: #fff !important;
            background-color: #91A060;
            font-size: 14px;
            text-decoration: none;
            text-transform: uppercase;
            display: inline-block;
        }
    }

    .im {
        color: inherit !important;
    }

    @media only screen and (max-width: 640px) {
        body {
            font-size: 16px;
        }

        table td {
            font-size: 16px;
        }

        table.body {
            .wrapper {
                padding: 15px !important;
            }

            .content {
                padding: 0 !important;
            }

            .container {
                box-sizing: border-box;
                padding: 5px 0 0 !important;
                width: 100% !important;
            }

            .main {
                display: block;
            }

            .btn {
                table {
                    width: 100% !important;
                }

                a {
                    width: 100% !important;
                }
            }
        }

        .footer td {
            padding: 15px 15px;
        }
    }

    @media (prefers-color-scheme: dark) {
        .logo-light {
            display: none !important;
        }

        .logo-dark {
            display: block !important;
        }
    }

    [data-ogsc] {
        .logo-light {
            display: none !important;
        }

        .logo-dark {
            display: block !important;
        }
    }
}
