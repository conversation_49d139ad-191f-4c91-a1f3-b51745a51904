<?xml version="1.0"?>
<!--
/**
 * @category  CookMedical
 * @package   CookMedical_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <preference for="Magento\Customer\Controller\Account\CreatePost"
                type="CookMedical\Customer\Controller\Account\CreatePost"/>
    <preference for="Magento\Customer\Block\Account\Dashboard\Address"
                type="CookMedical\Customer\Block\Account\Dashboard\Address"/>
    <preference for="Magento\Customer\Model\Address\Validator\General"
                type="CookMedical\Customer\Model\Address\Validator\General"/>
    <preference for="Magento\Customer\Model\Attribute\Data\Postcode"
                type="CookMedical\Customer\Model\Attribute\Data\Postcode"/>
    <preference for="Magento\Customer\Model\AccountManagement" type="CookMedical\Customer\Model\AccountManagement"/>
    <preference for="Magento\Customer\Model\Address\Validator\General"
                type="CookMedical\Customer\Model\Address\Validator\General"/>
    <preference for="Magento\Customer\Model\Attribute\Data\Postcode"
                type="CookMedical\Customer\Model\Attribute\Data\Postcode"/>
    <preference for="Magento\Customer\Model\AccountManagement" type="CookMedical\Customer\Model\AccountManagement"/>
    <type name="Magento\Customer\Controller\Account\LoginPost">
        <plugin name="after_execute_login" disabled="true"/>
    </type>
    <type name="Magento\Customer\Controller\Account\LoginPost">
        <plugin name="after_login" type="CookMedical\Customer\Plugin\LoginPostPlugin" sortOrder="20"/>
    </type>
</config>
