<?php
/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */

declare(strict_types=1);

namespace CookMedical\Checkout\CustomerData;

use Magento\Customer\CustomerData\SectionSourceInterface;
use Magento\Framework\Session\Generic as GenericSession;
use Psr\Log\LoggerInterface;

class LastVisitedPlp implements SectionSourceInterface
{
    /**
     * Session keys - following Magento core pattern
     */
    public const LAST_VISITED_PLP_URL_SESSION_KEY = 'cookmedical_last_plp_url';
    public const LAST_VISITED_PLP_DEBUG_SESSION_KEY = 'cookmedical_last_plp_debug';

    private GenericSession $session;
    private LoggerInterface $logger;

    public function __construct(
        GenericSession $session,
        LoggerInterface $logger
    ) {
        $this->session = $session;
        $this->logger = $logger;
    }

    /**
     * Get section data for customer data
     *
     * @return array
     */
    public function getSectionData(): array
    {
        $savedUrl = null;
        $debugInfo = null;

        try {
            $savedUrl = $this->session->getData(self::LAST_VISITED_PLP_URL_SESSION_KEY);
            $debugInfo = $this->session->getData(self::LAST_VISITED_PLP_DEBUG_SESSION_KEY);

            $this->logger->info('CookMedical CustomerData LastVisitedPlp: Retrieved data', [
                'saved_url' => $savedUrl,
                'has_debug_info' => $debugInfo !== null,
                'session_id' => $this->session->getSessionId()
            ]);

        } catch (\Exception $e) {
            $this->logger->error('CookMedical CustomerData LastVisitedPlp: Error retrieving data', [
                'error' => $e->getMessage()
            ]);
        }

        return [
            'last_visited_plp_url' => $savedUrl,
            'debug_info' => $debugInfo,
            'timestamp' => time()
        ];
    }
}
