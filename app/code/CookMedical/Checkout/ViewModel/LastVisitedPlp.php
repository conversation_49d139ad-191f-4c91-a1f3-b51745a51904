<?php
/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */

declare(strict_types=1);

namespace CookMedical\Checkout\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\Session\Generic as GenericSession;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Registry;
use Psr\Log\LoggerInterface;

class LastVisitedPlp implements ArgumentInterface
{
    /**
     * Session keys - following Magento core pattern
     */
    public const LAST_VISITED_PLP_URL_SESSION_KEY = 'cookmedical_last_plp_url';
    public const LAST_VISITED_PLP_DEBUG_SESSION_KEY = 'cookmedical_last_plp_debug';

    private const FALLBACK_URL = 'shop-all';

    private GenericSession $session;
    private UrlInterface $urlBuilder;
    private RequestInterface $request;
    private Registry $registry;
    private LoggerInterface $logger;

    public function __construct(
        GenericSession $session,
        UrlInterface $urlBuilder,
        RequestInterface $request,
        Registry $registry,
        LoggerInterface $logger
    ) {
        $this->session = $session;
        $this->urlBuilder = $urlBuilder;
        $this->request = $request;
        $this->registry = $registry;
        $this->logger = $logger;
    }

    public function getLastVisitedPlpUrl(): ?string
    {
        // Get data from Magento session storage - following core pattern
        $savedUrl = $this->session->getData(self::LAST_VISITED_PLP_URL_SESSION_KEY);

        $this->logger->info('CookMedical getLastVisitedPlpUrl: Retrieving from session', [
            'saved_url' => $savedUrl,
            'session_id' => $this->session->getSessionId(),
            'current_url' => $this->request->getRequestUri()
        ]);

        return $savedUrl;
    }

    public function hasLastVisitedPlp(): bool
    {
        $result = $this->getLastVisitedPlpUrl() !== null;
        $this->logger->info('CookMedical hasLastVisitedPlp: ' . ($result ? 'YES' : 'NO'));
        return $result;
    }

    public function getRedirectUrl(): string
    {
        $lastVisitedUrl = $this->getLastVisitedPlpUrl();

        if ($lastVisitedUrl) {
            $this->logger->info('CookMedical getRedirectUrl: Using saved URL', [
                'url' => $lastVisitedUrl
            ]);
            return $lastVisitedUrl;
        }

        // Fallback to shop-all page
        $fallbackUrl = $this->urlBuilder->getUrl(self::FALLBACK_URL);
        $this->logger->info('CookMedical getRedirectUrl: Using fallback URL', [
            'fallback_url' => $fallbackUrl
        ]);
        return $fallbackUrl;
    }

    public function getButtonLabel(): string
    {
        return __('Add More Products')->render();
    }

    public function saveCurrentPlpUrl(): void
    {
        $this->logger->info('CookMedical saveCurrentPlpUrl called');

        $moduleName = $this->request->getModuleName();
        $controllerName = $this->request->getControllerName();
        $actionName = $this->request->getActionName();
        $currentUrl = $this->request->getRequestUri();

        // Log current session state BEFORE any changes
        $currentSavedUrl = $this->session->getData(self::LAST_VISITED_PLP_URL_SESSION_KEY);

        $this->logger->info('CookMedical saveCurrentPlpUrl: Current session state BEFORE processing', [
            'current_saved_url' => $currentSavedUrl,
            'session_id' => $this->session->getSessionId()
        ]);

        // Log the current request details
        $this->logger->info('CookMedical SaveLastVisitedPlp: Request Details', [
            'module' => $moduleName,
            'controller' => $controllerName,
            'action' => $actionName,
            'current_url' => $currentUrl
        ]);

        $isPlpPage = $this->isPlpPage();
        $this->logger->info('CookMedical saveCurrentPlpUrl isPlpPage: ' . ($isPlpPage ? 'true' : 'false'));

        $this->logger->info('CookMedical SaveLastVisitedPlp: Is PLP Page?', [
            'result' => $isPlpPage ? 'YES' : 'NO',
            'current_category' => $this->registry->registry('current_category') ? 'SET' : 'NOT SET',
            'current_product' => $this->registry->registry('current_product') ? 'SET' : 'NOT SET'
        ]);

        // IMPORTANT: Don't override existing PLP data if current page is NOT a PLP
        if (!$isPlpPage) {
            $this->logger->info('CookMedical SaveLastVisitedPlp: Not a PLP page, preserving existing session data');
            return;
        }

        // Save if it's a PLP page - FOLLOWING MAGENTO CORE PATTERN
        if ($isPlpPage) {
            $fullUrl = $this->urlBuilder->getUrl('*/*/*', ['_current' => true, '_use_rewrite' => true]);

            $this->logger->info('CookMedical SaveLastVisitedPlp: Saving URL', [
                'current_url' => $currentUrl,
                'full_url' => $fullUrl
            ]);

            $debugInfo = [
                'module' => $moduleName,
                'controller' => $controllerName,
                'action' => $actionName,
                'current_url' => $currentUrl,
                'full_url' => $fullUrl,
                'category_id' => $this->registry->registry('current_category') ? $this->registry->registry('current_category')->getId() : null,
                'timestamp' => time(),
                'session_id' => $this->session->getSessionId()
            ];

            // Save data following Magento core pattern - NO writeClose/start!
            $this->session->setData(self::LAST_VISITED_PLP_URL_SESSION_KEY, $fullUrl);
            $this->session->setData(self::LAST_VISITED_PLP_DEBUG_SESSION_KEY, $debugInfo);

            $this->logger->info('CookMedical SaveLastVisitedPlp: URL Saved to Session', [
                'saved_url' => $fullUrl,
                'debug_info' => $debugInfo
            ]);

            // Verify it was saved immediately - simple getData() call
            $verifyUrl = $this->session->getData(self::LAST_VISITED_PLP_URL_SESSION_KEY);
            $verifyDebug = $this->session->getData(self::LAST_VISITED_PLP_DEBUG_SESSION_KEY);

            $this->logger->info('CookMedical SaveLastVisitedPlp: IMMEDIATE Verification', [
                'saved_url' => $verifyUrl,
                'debug_info' => $verifyDebug,
                'save_successful' => $verifyUrl ? 'YES' : 'NO'
            ]);
        }
    }

    private function isPlpPage(): bool
    {
        // Method 1: Use Magento Registry (most reliable)
        $currentCategory = $this->registry->registry('current_category');
        $currentProduct = $this->registry->registry('current_product');

        // If we have a category but no product, it's a PLP
        $isPlpByRegistry = $currentCategory && !$currentProduct;

        // Method 2: Check controller/action (fallback)
        $moduleName = $this->request->getModuleName();
        $controllerName = $this->request->getControllerName();
        $actionName = $this->request->getActionName();

        $isPlpByController = $moduleName === 'catalog' &&
                            $controllerName === 'category' &&
                            $actionName === 'view';

        // Method 3: Check if it's a search results page (also a type of PLP)
        $isSearchResults = $moduleName === 'catalogsearch' &&
                          $controllerName === 'result' &&
                          $actionName === 'index';

        return $isPlpByRegistry || $isPlpByController || $isSearchResults;
    }

    // Add this method for debugging
    public function getDebugInfo(): array
    {
        $savedUrl = $this->session->getData(self::LAST_VISITED_PLP_URL_SESSION_KEY);
        $debugInfo = $this->session->getData(self::LAST_VISITED_PLP_DEBUG_SESSION_KEY);

        $this->logger->info('CookMedical getDebugInfo: Session data retrieved', [
            'saved_url' => $savedUrl,
            'debug_info' => $debugInfo,
            'session_id' => $this->session->getSessionId()
        ]);

        $currentRequest = [
            'module' => $this->request->getModuleName(),
            'controller' => $this->request->getControllerName(),
            'action' => $this->request->getActionName(),
            'current_url' => $this->request->getRequestUri()
        ];

        $registryInfo = [
            'current_category' => $this->registry->registry('current_category') ? $this->registry->registry('current_category')->getName() : 'NULL',
            'current_product' => $this->registry->registry('current_product') ? $this->registry->registry('current_product')->getName() : 'NULL'
        ];

        return [
            'saved_url' => $savedUrl,
            'debug_info' => $debugInfo,
            'current_request' => $currentRequest,
            'registry_info' => $registryInfo,
            'is_plp_page' => $this->isPlpPage(),
            'session_id' => $this->session->getSessionId()
        ];
    }
}
