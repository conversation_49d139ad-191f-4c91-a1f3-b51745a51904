<?php
/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2025 Scandiweb, Inc (http://scandiweb.com)
 */

declare(strict_types=1);

namespace CookMedical\Checkout\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\UrlInterface;

class LastVisitedPlp implements ArgumentInterface
{
    private const FALLBACK_URL = 'shop-all';

    private UrlInterface $urlBuilder;

    public function __construct(UrlInterface $urlBuilder)
    {
        $this->urlBuilder = $urlBuilder;
    }

    public function getRedirectUrl(): string
    {
        return $this->urlBuilder->getUrl(self::FALLBACK_URL);
    }

    public function getButtonLabel(): string
    {
        return __('Add More Products')->render();
    }
}
