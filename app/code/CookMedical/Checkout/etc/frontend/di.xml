<?xml version="1.0"?>
<!--
/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <!-- Register customer data section for last visited PLP -->
    <type name="Magento\Customer\CustomerData\SectionPoolInterface">
        <arguments>
            <argument name="sectionSourceMap" xsi:type="array">
                <item name="cookmedical_last_visited_plp" xsi:type="string">CookMedical\Checkout\CustomerData\LastVisitedPlp</item>
            </argument>
        </arguments>
    </type>
    
</config>
