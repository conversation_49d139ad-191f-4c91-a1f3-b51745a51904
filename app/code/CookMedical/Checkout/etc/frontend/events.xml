<?xml version="1.0"?>
<!--
/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <!-- Listen to category view events -->
    <event name="controller_action_postdispatch_catalog_category_view">
        <observer name="cookmedical_save_last_visited_plp" instance="CookMedical\Checkout\Observer\SaveLastVisitedPlp"/>
    </event>
    <!-- Also listen to search results (another type of PLP) -->
    <event name="controller_action_postdispatch_catalogsearch_result_index">
        <observer name="cookmedical_save_last_visited_plp_search" instance="CookMedical\Checkout\Observer\SaveLastVisitedPlp"/>
    </event>
</config>
