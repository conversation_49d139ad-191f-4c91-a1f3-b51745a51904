/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */

var config = {
    config: {
        mixins: {
            'Magento_OfflinePayments/js/view/payment/method-renderer/purchaseorder-method': {
                'CookMedical_Checkout/js/view/payment/method-renderer/purchaseorder-method-mixin': true
            },
            'Magento_Checkout/js/view/payment/default': {
                'CookMedical_Checkout/js/view/payment/default-mixin': true
            },
            'Magento_Checkout/js/view/progress-bar': {
                'CookMedical_Checkout/js/view/progress-bar-mixin': true
            },
            'Magento_Checkout/js/view/summary/abstract-total': {
                'CookMedical_Checkout/js/view/summary/abstract-total-mixin': true
            },
            'Magento_Checkout/js/view/shipping-address/address-renderer/default': {
                'CookMedical_Checkout/js/view/shipping-address/address-renderer/default-mixin': true
            },
            'Magento_Checkout/js/view/summary/item/details': {
                'CookMedical_Checkout/js/view/summary/item/details-mixin': true
            },
            'Magento_Checkout/js/view/summary': {
                'CookMedical_Checkout/js/view/summary-mixin': true
            },
            'Rootways_CardConnect/js/view/payment/method-renderer/cardconnect-method': {
                'CookMedical_Checkout/js/view/payment/method-renderer/cardconnect-method-mixin': true
            },
            'Magento_Checkout/js/model/payment/method-group': {
                'CookMedical_Checkout/js/model/payment/method-group-mixin': true
            },
            'Magento_Checkout/js/view/billing-address': {
                'CookMedical_Checkout/js/view/billing-address-mixin': true
            },
            'Magento_Payment/js/view/payment/cc-form': {
                'CookMedical_Checkout/js/view/payment/cc-form-mixin': true
            },
            'mage/validation': {
                'CookMedical_Checkout/js/view/payment/validate-po-number': true
            },
            'Magento_Catalog/js/catalog-add-to-cart': {
                'CookMedical_Checkout/js/catalog-add-to-cart-mixin': true
            },
            'Magento_Checkout/js/view/summary/shipping': {
                'CookMedical_Checkout/js/view/summary/shipping-mixin': true
            }
        }
    },
    map: {
        '*': {
            'Magento_Checkout/template/sidebar': 'CookMedical_Checkout/template/sidebar',
            'Magento_Checkout/template/summary/cart-items': 'CookMedical_Checkout/template/summary/cart-items',
            "accountCartRefresher": 'CookMedical_Checkout/js/account-cart-refresher',
            "lastVisitedPlpStorage": 'CookMedical_Checkout/js/last-visited-plp-storage'
        }
    }
};
