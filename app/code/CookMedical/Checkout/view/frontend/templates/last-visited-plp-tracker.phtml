<?php
/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */

/**
 * Template to initialize PLP URL tracking in localStorage
 * Similar to recently_viewed_product functionality
 */
?>

<script>
require([
    'CookMedical_Checkout/js/last-visited-plp-tracker'
], function (plpTracker) {
    'use strict';
    
    // Initialize the tracker - it will automatically detect PLP pages and save URLs
    plpTracker.init();
});
</script>
