define([
    'jquery',
    'ko',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/totals',
    'Magento_Checkout/js/action/get-totals'
], function ($, ko, quote, totalsModel, getTotalsAction) {
    'use strict';

    return {
        /**
         * Update shipping costs in real-time when shipping method changes
         * @param {Object} shippingMethod
         */
        updateShippingCost: function (shippingMethod) {
            const self = this;
            
            console.log('CookMedical: Starting shipping cost update for method:', 
                shippingMethod.carrier_code + '_' + shippingMethod.method_code);

            // Set the shipping method in quote
            quote.shippingMethod(shippingMethod);

            // Trigger totals recalculation
            this.recalculateTotals().then(function () {
                console.log('CookMedical: Shipping cost update completed');
                
                // Force UI update
                self.forceUIUpdate();
            }).catch(function (error) {
                console.error('CookMedical: Error updating shipping cost:', error);
            });
        },

        /**
         * Recalculate totals
         * @returns {Promise}
         */
        recalculateTotals: function () {
            return new Promise(function (resolve, reject) {
                try {
                    // Show loading state
                    totalsModel.isLoading(true);

                    // Get updated totals from server
                    const deferred = getTotalsAction([], quote.getQuoteId());
                    
                    deferred.done(function (totals) {
                        // Update totals model
                        quote.setTotals(totals);
                        totalsModel.isLoading(false);
                        resolve(totals);
                    }).fail(function (error) {
                        totalsModel.isLoading(false);
                        reject(error);
                    });
                    
                } catch (error) {
                    totalsModel.isLoading(false);
                    reject(error);
                }
            });
        },

        /**
         * Force UI components to update
         */
        forceUIUpdate: function () {
            // Force shipping summary component to re-render
            const shippingElements = $('.totals.shipping .price');
            if (shippingElements.length > 0) {
                // Trigger a visual update
                shippingElements.addClass('updating');
                setTimeout(function () {
                    shippingElements.removeClass('updating');
                }, 300);
            }

            // Trigger custom event for other components to listen to
            $(document).trigger('cookmedical:shipping-cost-updated', {
                shippingMethod: quote.shippingMethod(),
                totals: quote.totals()
            });
        },

        /**
         * Initialize the updater
         */
        init: function () {
            const self = this;
            
            // Listen for shipping method changes globally
            quote.shippingMethod.subscribe(function (shippingMethod) {
                if (shippingMethod && shippingMethod.carrier_code && shippingMethod.method_code) {
                    // Small delay to ensure the method is properly set
                    setTimeout(function () {
                        self.updateShippingCost(shippingMethod);
                    }, 50);
                }
            });

            console.log('CookMedical: Shipping cost updater initialized');
        }
    };
});
