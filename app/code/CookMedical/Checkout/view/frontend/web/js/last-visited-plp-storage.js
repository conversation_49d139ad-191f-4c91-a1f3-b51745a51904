define([
    'jquery',
    'Magento_Ui/js/lib/core/storage/local',
    'Magento_Customer/js/customer-data',
    'uiRegistry'
], function ($, localStorage, customerData, registry) {
    'use strict';

    var lastVisitedPlpStorage = {
        storageKey: 'cookmedical_last_visited_plp',
        storageExpiryTime: 86400, // 24 hours in seconds
        locStorage: registry.get('localStorage'),

        /**
         * Initialize the storage handler
         */
        init: function() {
            var self = this;
            
            // Listen for customer data updates
            var customerDataSection = customerData.get('cookmedical_last_visited_plp');
            
            customerDataSection.subscribe(function(data) {
                if (data && data.last_visited_plp_url) {
                    self.setItem(self.storageKey, {
                        url: data.last_visited_plp_url,
                        timestamp: data.timestamp || Math.floor(Date.now() / 1000)
                    });
                    console.log('CookMedical: Saved PLP URL to localStorage:', data.last_visited_plp_url);
                }
            });

            // Check if we have data in localStorage that should be restored to session
            this.restoreFromLocalStorage();
        },

        /**
         * Set item in localStorage with expiry
         */
        setItem: function(key, value) {
            var storedValue = {
                expiryTime: Math.floor(Date.now() / 1000) + this.storageExpiryTime,
                value: value
            };

            try {
                this.locStorage.set(key, storedValue);
                console.log('CookMedical: Stored in localStorage:', key, storedValue);
            } catch (e) {
                console.error('CookMedical: Error storing in localStorage:', e);
            }
        },

        /**
         * Get item from localStorage
         */
        getItem: function(key) {
            try {
                var storedData = this.locStorage.get(key);
                
                if (!storedData || !storedData.expiryTime || !storedData.value) {
                    return null;
                }

                var currentTime = Math.floor(Date.now() / 1000);
                
                if (currentTime > storedData.expiryTime) {
                    // Data has expired, remove it
                    this.removeItem(key);
                    return null;
                }

                return storedData.value;
            } catch (e) {
                console.error('CookMedical: Error retrieving from localStorage:', e);
                return null;
            }
        },

        /**
         * Remove item from localStorage
         */
        removeItem: function(key) {
            try {
                this.locStorage.remove(key);
            } catch (e) {
                console.error('CookMedical: Error removing from localStorage:', e);
            }
        },

        /**
         * Restore data from localStorage if session is empty
         */
        restoreFromLocalStorage: function() {
            var self = this;
            var customerDataSection = customerData.get('cookmedical_last_visited_plp');
            
            // Check if customer data section is empty
            if (!customerDataSection() || !customerDataSection().last_visited_plp_url) {
                var localData = this.getItem(this.storageKey);
                
                if (localData && localData.url) {
                    console.log('CookMedical: Restoring PLP URL from localStorage:', localData.url);
                    
                    // Trigger an AJAX call to restore the session data
                    $.ajax({
                        url: '/cookmedical/checkout/restore-plp-url',
                        type: 'POST',
                        data: {
                            url: localData.url,
                            timestamp: localData.timestamp
                        },
                        success: function(response) {
                            console.log('CookMedical: Successfully restored PLP URL to session');
                            // Reload customer data section
                            customerData.reload(['cookmedical_last_visited_plp']);
                        },
                        error: function(xhr, status, error) {
                            console.error('CookMedical: Error restoring PLP URL to session:', error);
                        }
                    });
                }
            }
        },

        /**
         * Get the current PLP URL from either customer data or localStorage
         */
        getCurrentPlpUrl: function() {
            var customerDataSection = customerData.get('cookmedical_last_visited_plp');
            
            // First try customer data
            if (customerDataSection() && customerDataSection().last_visited_plp_url) {
                return customerDataSection().last_visited_plp_url;
            }
            
            // Fallback to localStorage
            var localData = this.getItem(this.storageKey);
            return localData ? localData.url : null;
        }
    };

    return lastVisitedPlpStorage;
});
