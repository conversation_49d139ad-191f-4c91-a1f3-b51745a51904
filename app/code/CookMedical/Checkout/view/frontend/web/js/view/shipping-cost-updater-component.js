define([
    'uiComponent',
    'CookMedical_Checkout/js/shipping-cost-updater'
], function (Component, shippingCostUpdater) {
    'use strict';

    return Component.extend({
        
        /**
         * Initialize component
         */
        initialize: function () {
            this._super();
            
            // Initialize the shipping cost updater
            shippingCostUpdater.init();
            
            console.log('CookMedical: Shipping cost updater component initialized');
            
            return this;
        }
    });
});
