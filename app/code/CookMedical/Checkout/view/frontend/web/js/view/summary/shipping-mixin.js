define([
    'ko',
    'Magento_Checkout/js/model/quote'
], function (ko, quote) {
    'use strict';

    return function (Component) {
        return Component.extend({

            /**
             * Initialize component
             */
            initialize: function () {
                this._super();

                // Subscribe to shipping method changes for real-time updates
                this.setupShippingMethodListener();

                return this;
            },

            /**
             * Setup listener for shipping method changes
             */
            setupShippingMethodListener: function () {
                const self = this;

                // Listen for shipping method changes
                quote.shippingMethod.subscribe(function (shippingMethod) {
                    if (shippingMethod) {
                        console.log('CookMedical: Shipping method changed, updating sidebar cost:',
                            shippingMethod.carrier_code + '_' + shippingMethod.method_code);

                        // Force component to re-evaluate
                        self.isCalculated.valueHasMutated();
                    }
                });
            },

            /**
             * Override to return empty string instead of message
             * @return {*}
            */
            getValue: function () {
                let price;

                if (!this.isCalculated()) {
                    return "";
                }
                price = this.totals()['shipping_amount'];

                return this.getFormattedPrice(price);
            }

        });
    };
});
