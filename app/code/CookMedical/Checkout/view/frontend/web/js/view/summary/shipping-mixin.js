define([], function () {
    'use strict';

    return function (Component) {
        return Component.extend({
            /**
             * Override to return empty string instead of message
             * @return {*}
            */
            getValue: function () {
                let price;

                if (!this.isCalculated()) {
                    return "";
                }
                price = this.totals()['shipping_amount'];

                return this.getFormattedPrice(price);
            }

        });
    };
});
