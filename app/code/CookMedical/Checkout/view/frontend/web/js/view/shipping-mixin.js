define([
    'jquery',
    'Magento_Checkout/js/action/get-totals',
    'Magento_Checkout/js/model/quote'
], function ($, getTotalsAction, quote) {
    'use strict';

    return function (Component) {
        return Component.extend({

            /**
             * Initialize component with debug logging
             */
            initialize: function () {
                console.log('CookMedical: Shipping mixin initialized');
                return this._super();
            },

            /**
             * Override selectShippingMethod to add real-time shipping cost updates
             * @param {Object} shippingMethod
             * @return {Boolean}
             */
            selectShippingMethod: function (shippingMethod) {
                console.log('CookMedical: selectShippingMethod called with mixin', shippingMethod);

                // Call the parent method first
                const result = this._super(shippingMethod);

                // Add our shipping cost update logic
                this.updateShippingCostInSidebar(shippingMethod);

                return result;
            },

            /**
             * Update shipping cost in sidebar without page reload
             * @param {Object} shippingMethod
             */
            updateShippingCostInSidebar: function (shippingMethod) {
                console.log('CookMedical: Updating shipping cost for method:',
                    shippingMethod.carrier_code + '_' + shippingMethod.method_code);

                // Get updated totals which will automatically update the sidebar
                const deferred = getTotalsAction([], quote.getQuoteId());
                deferred.done(function (totals) {
                    quote.setTotals(totals);
                    console.log('CookMedical: Shipping cost updated successfully in sidebar');
                }).fail(function (error) {
                    console.error('CookMedical: Error updating shipping cost:', error);
                });
            }
        });
    };
});
