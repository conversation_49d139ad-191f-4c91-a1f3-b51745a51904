define([
    'jquery'
], function ($) {
    'use strict';

    return {
        storageKey: 'cookmedical_last_plp_url',
        
        /**
         * Save current PLP URL to localStorage
         * This runs on PLP pages, similar to recently_viewed_product
         */
        saveCurrentPlpUrl: function() {
            try {
                var currentUrl = window.location.href;
                
                // Save to localStorage with timestamp
                var dataToStore = {
                    url: currentUrl,
                    timestamp: Math.floor(Date.now() / 1000)
                };
                
                localStorage.setItem(this.storageKey, JSON.stringify(dataToStore));
                console.log('CookMedical: Saved current PLP URL to localStorage:', currentUrl);
                
            } catch (e) {
                console.error('CookMedical: Error saving PLP URL to localStorage:', e);
            }
        },
        
        /**
         * Get PLP URL from localStorage
         */
        getStoredPlpUrl: function() {
            try {
                var storedData = localStorage.getItem(this.storageKey);
                if (storedData) {
                    var parsedData = JSON.parse(storedData);
                    return parsedData.url;
                }
            } catch (e) {
                console.error('CookMedical: Error reading PLP URL from localStorage:', e);
            }
            return null;
        },
        
        /**
         * Initialize the tracker on PLP pages
         */
        init: function() {
            // Check if we're on a PLP page (category page)
            if (this.isPlpPage()) {
                this.saveCurrentPlpUrl();
            }
        },
        
        /**
         * Check if current page is a PLP page
         */
        isPlpPage: function() {
            // Check URL patterns that indicate PLP pages
            var currentPath = window.location.pathname;
            
            // Common PLP page patterns
            var plpPatterns = [
                '/shop-all',
                '/category/',
                '/catalog/category/'
            ];
            
            for (var i = 0; i < plpPatterns.length; i++) {
                if (currentPath.indexOf(plpPatterns[i]) !== -1) {
                    return true;
                }
            }
            
            // Also check if body has category-specific classes (Magento standard)
            var bodyClasses = document.body.className;
            if (bodyClasses.indexOf('catalog-category-view') !== -1) {
                return true;
            }
            
            return false;
        }
    };
});
