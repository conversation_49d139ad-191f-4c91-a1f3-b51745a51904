define([
    'jquery'
], function ($) {
    'use strict';

    return {
        storageKey: 'cookmedical_last_plp_url',

        /**
         * Save current PLP URL to localStorage
         * This runs on PLP pages, similar to recently_viewed_product
         */
        saveCurrentPlpUrl: function() {
            try {
                localStorage.setItem(this.storageKey, JSON.stringify({
                    url: window.location.href,
                    timestamp: Math.floor(Date.now() / 1000)
                }));
            } catch (e) {
                console.error('CookMedical: Error saving PLP URL:', e);
            }
        },

        /**
         * Get PLP URL from localStorage
         */
        getStoredPlpUrl: function() {
            try {
                var storedData = localStorage.getItem(this.storageKey);
                if (storedData) {
                    return JSON.parse(storedData).url;
                }
            } catch (e) {
                console.error('CookMedical: Error reading PLP URL:', e);
            }
            return null;
        },

        /**
         * Initialize the tracker on PLP pages
         */
        init: function() {
            if (this.isPlpPage()) {
                this.saveCurrentPlpUrl();
            }
        },

        /**
         * Check if current page is a PLP page
         */
        isPlpPage: function() {
            var currentPath = window.location.pathname;
            var plpPatterns = ['/shop-all', '/category/', '/catalog/category/'];

            return plpPatterns.some(function(pattern) {
                return currentPath.indexOf(pattern) !== -1;
            }) || document.body.className.indexOf('catalog-category-view') !== -1;
        }
    };
});
