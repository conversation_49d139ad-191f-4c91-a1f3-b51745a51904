<?php
/**
 * @category  CookMedical
 * @package   CookMedical_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2024 Scandiweb, Inc (http://scandiweb.com)
 */

declare(strict_types=1);

namespace CookMedical\Checkout\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use CookMedical\Checkout\ViewModel\LastVisitedPlp;
use Psr\Log\LoggerInterface;

class SaveLastVisitedPlp implements ObserverInterface
{
    private LastVisitedPlp $lastVisitedPlp;
    private LoggerInterface $logger;

    public function __construct(
        LastVisitedPlp $lastVisitedPlp,
        LoggerInterface $logger
    ) {
        $this->lastVisitedPlp = $lastVisitedPlp;
        $this->logger = $logger;
    }

    public function execute(Observer $observer): void
    {
        // Log that observer is triggered
        $this->logger->info('CookMedical SaveLastVisitedPlp Observer: TRIGGERED');

        try {
            $this->lastVisitedPlp->saveCurrentPlpUrl();
            $this->logger->info('CookMedical SaveLastVisitedPlp Observer: saveCurrentPlpUrl() completed');
        } catch (\Exception $e) {
            $this->logger->error('CookMedical SaveLastVisitedPlp Observer: ERROR - ' . $e->getMessage());
        }
    }
}
