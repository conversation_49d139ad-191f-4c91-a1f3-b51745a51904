<?xml version="1.0"?>
<!--
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="weltpixel" translate="label" sortOrder="2000" class="weltpixel-tab">
            <label>WeltPixel</label>
        </tab>
        <section id="weltpixel_backend_config" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>WeltPixel Documentation</label>
            <tab>weltpixel</tab>
            <class>item-weltpixel-documentation</class>
            <resource>WeltPixel_Backend::DocumentationSettings</resource>
            <group id="recommended_solutions" translate="label" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Recommended Solutions:</label>
                <frontend_model>WeltPixel\Backend\Block\Adminhtml\System\Config\Extensions</frontend_model>
            </group>
        </section>
        <section id="weltpixel_backend_developer" translate="label" type="text" sortOrder="1000" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Developer</label>
            <tab>weltpixel</tab>
            <resource>WeltPixel_Backend::DeveloperSettings</resource>
            <group id="general_module_information" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>WeltPixel Backend</label>
                <attribute type="expanded">1</attribute>
                <fieldset_css>weltpixel-module-information-header active</fieldset_css>
                <field id="separator_product_dimensions" translate="button_label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>WeltPixel\Backend\Block\Adminhtml\System\Config\ModuleInformation</frontend_model>
                    <button_label>WeltPixel_Backend</button_label>
                </field>
            </group>
            <group id="system_information" translate="label" type="text" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>System Information</label>
                <field id="infromation_details" translate="button_label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>WeltPixel\Backend\Block\Adminhtml\System\Config\SystemInformation</frontend_model>
                </field>
            </group>
            <group id="notifications" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Notifications</label>
                <field id="enable_admin_notification" translate="label" type="select" sortOrder="0" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable WeltPixel Admin Notifications</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable to receive notifications about important Product Updates via the Magento Admin.</comment>
                </field>
            </group>
            <group id="csp" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Content Security Policies</label>
                <field id="change_system_value" translate="label comment" type="select" sortOrder="0" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Change Magento Default Mode</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Only available from 2.3.5 version.</comment>
                </field>
                <field id="report_only" translate="label" type="select" sortOrder="0" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Report Only</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="change_system_value">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
