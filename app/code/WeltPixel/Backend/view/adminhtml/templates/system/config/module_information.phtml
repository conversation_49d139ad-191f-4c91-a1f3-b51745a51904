<?php
$originalData = $block->getData('element_data');
$moduleName = $originalData['button_label'] ?? '';

/** @var  \WeltPixel\Backend\Helper\Developer $_helper */
$_helper = $this->helper('WeltPixel\Backend\Helper\Developer');

$moduleVersion = $_helper->getComposerVersion($moduleName);
$moduleLatestVersion = $_helper->getModuleLatestVersion($moduleName);
$isModuleUpToDate = false;
$messageClass = 'message-warning';

if (version_compare($moduleLatestVersion, $moduleVersion) < 1) {
    $isModuleUpToDate = true;
    $messageClass = 'message-success';
}
?>

<div class="message <?= $messageClass ?>">
    <?php if ($isModuleUpToDate) : ?>
    <p>The module is up to date, you have the latest version.</p>
    <?php else : ?>
        <p>Update is available and recommended. Current version is: <strong><?= $moduleVersion ?></strong>. Download the latest version of the module from the following <a target="_blank" href="https://www.weltpixel.com/downloadable/customer/products/">link</a>.</p>
    <?php endif; ?>
</div>
<div class="request-feature-button">
    <a target="_blank" id="request_feature" title="Request Feature" class="action-default scalable save primary ui-button ui-corner-all ui-widget" href="https://support.weltpixel.com/hc/en-us/requests/new">Submit a feature request</a>
</div>

<script>
    requirejs(['jquery'],function($) {
        $(document).ready(function() {
            let currentSectionTitleElm = $('.section-config.weltpixel-module-information-header .entry-edit-head a');
            let currentSectionTitle = currentSectionTitleElm.html();
            currentSectionTitleElm.html(currentSectionTitle + ' - <span class="wp-module-version"> <?= $moduleVersion ?></span>');
        });
    });
</script>
