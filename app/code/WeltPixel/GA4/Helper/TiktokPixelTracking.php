<?php

namespace WeltPixel\GA4\Helper;

/**
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class TiktokPixelTracking extends Data
{
    /**
     * @return boolean
     */
    public function isTiktokPixelTrackingEnabled() {
        return $this->_gtmOptions['tiktok_pixel_tracking']['enable'];
    }

    /**
     * @return string
     */
    public function getTiktokPixelTrackingCodeSnippet() {
        return trim($this->_gtmOptions['tiktok_pixel_tracking']['code_snippet'] ?? '');
    }


    /**
     * @return array
     */
    public function getTiktokPixelTrackedEvents() {
        $trackedEvents = $this->_gtmOptions['tiktok_pixel_tracking']['events'] ?? '';
        return explode(',', $trackedEvents);
    }

    /**
     * @param string $eventName
     * @return bool
     */
    public function shouldTiktokPixelEventBeTracked($eventName) {
        $availableEvents = $this->getTiktokPixelTrackedEvents();
        return in_array($eventName, $availableEvents);
    }

    /**
     * @param $product
     * @return array
     */
    public function tiktokPixelAddToWishlistPushData($product)
    {
        $result = [
            'eventName' => \WeltPixel\GA4\Model\Config\Source\TiktokPixel\TrackingEvents::EVENT_ADD_TO_WISHLIST,
            'eventData' => [
                'value' => floatval(number_format($product->getPriceInfo()->getPrice('final_price')->getValue(), 2, '.', '')),
                'currency' => $this->getCurrencyCode(),
                'contents' => [
                    [
                        'content_id' => $this->getTiktokProductId($product),
                        'content_type' => 'product',
                        'content_name' => html_entity_decode($product->getName() ?? '')
                    ]
                ]
            ]
        ];

        return $result;
    }

    /**
     * @param $product
     * @param int $qty
     * @return array
     */
    public function tiktokPixelAddToCartPushData($product, $qty = 1)
    {
        $result = [
            'eventName' => \WeltPixel\GA4\Model\Config\Source\TiktokPixel\TrackingEvents::EVENT_ADD_TO_CART,
            'eventData' => [
                'value' => floatval(number_format($product->getPriceInfo()->getPrice('final_price')->getValue(), 2, '.', '')),
                'currency' => $this->getCurrencyCode(),
                'quantity' => $qty,
                'contents' => [
                    [
                        'content_id' => $this->getTiktokProductId($product),
                        'content_type' => 'product',
                        'quantity' => $qty,
                        'content_name' => html_entity_decode($product->getName() ?? '')
                    ]
                ]
            ]
        ];

        return $result;
    }

    /**
     * Returns the product id or sku based on the backend settings
     * @param \Magento\Catalog\Model\Product $product
     * @return string
     */
    public function getTiktokProductId($product)
    {
        $idOption = $this->_gtmOptions['tiktok_pixel_tracking']['id_selection'];
        $metaProductId = '';

        switch ($idOption) {
            case 'sku':
                $metaProductId = $product->getData('sku');
                break;
            case 'id':
            default:
                $metaProductId = $product->getId();
                break;
        }

        return $metaProductId;
    }

}
