<?php

namespace WeltPixel\GA4\Helper;

/**
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class RedditPixelTracking extends Data
{
    /**
     * @return boolean
     */
    public function isRedditPixelTrackingEnabled() {
        return $this->_gtmOptions['reddit_pixel_tracking']['enable'];
    }

    /**
     * @return string
     */
    public function getRedditPixelCodeSnippet() {
        return trim($this->_gtmOptions['reddit_pixel_tracking']['code_snippet'] ?? '');
    }


    /**
     * @return array
     */
    public function getRedditPixelTrackedEvents() {
        $trackedEvents = $this->_gtmOptions['reddit_pixel_tracking']['events'] ?? '';
        return explode(',', $trackedEvents);
    }

    /**
     * @param string $eventName
     * @return bool
     */
    public function shouldRedditPixelEventBeTracked($eventName) {
        $availableEvents = $this->getRedditPixelTrackedEvents();
        return in_array($eventName, $availableEvents);
    }

    /**
     * @param $product
     * @return array
     */
    public function redditPixelAddToWishlistPushData($product)
    {
        $result = [
            'track' => 'track',
            'eventName' => \WeltPixel\GA4\Model\Config\Source\RedditPixel\TrackingEvents::EVENT_ADD_TO_WISHLIST,
            'eventData' => [
                'value' => floatval(number_format($product->getPriceInfo()->getPrice('final_price')->getValue(), 2, '.', '')),
                'currency' => $this->getCurrencyCode(),
                'products' => [
                    [
                        'id' => $this->getRedditProductId($product),
                        'name' => html_entity_decode($product->getName() ?? ''),
                        'category' => addslashes(str_replace('"','&quot;',$this->getContentCategory($product->getCategoryIds())))
                    ]
                ]
            ]
        ];

        return $result;
    }

    /**
     * @param $product
     * @param int $qty
     * @return array
     */
    public function redditPixelAddToCartPushData($product, $qty = 1)
    {
        $result = [
            'track' => 'track',
            'eventName' => \WeltPixel\GA4\Model\Config\Source\RedditPixel\TrackingEvents::EVENT_ADD_TO_CART,
            'eventData' => [
                'value' => floatval(number_format($product->getPriceInfo()->getPrice('final_price')->getValue(), 2, '.', '')),
                'currency' => $this->getCurrencyCode(),
                'itemCount' => $qty,
                'products' => [
                    [
                        'id' => $this->getRedditProductId($product),
                        'name' => html_entity_decode($product->getName() ?? ''),
                        'category' => addslashes(str_replace('"','&quot;',$this->getContentCategory($product->getCategoryIds())))
                    ]
                ]
            ]
        ];

        return $result;
    }

    /**
     * Returns the product id or sku based on the backend settings
     * @param \Magento\Catalog\Model\Product $product
     * @return string
     */
    public function getRedditProductId($product)
    {
        $idOption = $this->_gtmOptions['reddit_pixel_tracking']['id_selection'];
        $metaProductId = '';

        switch ($idOption) {
            case 'sku':
                $metaProductId = $product->getData('sku');
                break;
            case 'id':
            default:
                $metaProductId = $product->getId();
                break;
        }

        return $metaProductId;
    }

    /**
     * @param array $categoryIds
     * @return string
     */
    public function getContentCategory($categoryIds)
    {
        $categoriesArray = $this->getGA4CategoriesFromCategoryIds($categoryIds);
        return implode(", ", $categoriesArray);
    }

}
