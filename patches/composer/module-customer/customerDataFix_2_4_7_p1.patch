Index: vendor/magento/module-customer/view/frontend/templates/js/customer-data.phtml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/magento/module-customer/view/frontend/templates/js/customer-data.phtml b/vendor/magento/module-customer/view/frontend/templates/js/customer-data.phtml
--- a/vendor/magento/module-customer/view/frontend/templates/js/customer-data.phtml	
+++ b/vendor/magento/module-customer/view/frontend/templates/js/customer-data.phtml	
@@ -10,15 +10,13 @@
 /** @var \Magento\Customer\Block\CustomerData $block */

 // phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
-/** @var Auth $auth */
-$auth = $block->getAuth() ?? ObjectManager::getInstance()->get(Auth::class);
-/** @var JsonSerializer $jsonSerializer */
+/** @var \Magento\Customer\ViewModel\Customer\Auth $auth */
+$auth = $block->getAuth() ?? ObjectManager::getInstance()->get(\Magento\Customer\ViewModel\Customer\Auth::class);
+/** @var \Magento\Customer\ViewModel\Customer\JsonSerializer $jsonSerializer */
 $jsonSerializer = $block->getJsonSerializer() ??
-    ObjectManager::getInstance()->get(JsonSerializer::class);
+    ObjectManager::getInstance()->get(\Magento\Customer\ViewModel\Customer\JsonSerializer::class);
 $customerDataUrl = $block->getCustomerDataUrl('customer/account/updateSession');
 $expirableSectionNames = $block->getExpirableSectionNames();
-/** @var CookieSettings $cookieSettings */
-$cookieSettings = $block->getCookieSettings();
 ?>
 <script type="text/x-magento-init">
     {
@@ -30,7 +28,6 @@
                     $expirableSectionNames
                 ) ?>,
                 "cookieLifeTime": "<?= $block->escapeJs($block->getCookieLifeTime()) ?>",
-                "cookieDomain": "<?= $block->escapeJs($cookieSettings->getCookieDomain()) ?>",
                 "updateSessionUrl": "<?= $block->escapeJs($customerDataUrl) ?>",
                 "isLoggedIn": "<?= /* @noEscape */ $auth->isLoggedIn() ?>"
             }
